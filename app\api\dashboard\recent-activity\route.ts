import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const tenantId = session.user.tenantId

    // Get recent posts
    const recentPosts = await prisma.post.findMany({
      where: { tenantId },
      include: { user: { select: { name: true } } },
      orderBy: { createdAt: "desc" },
      take: 5,
    })

    // Get recent campaigns
    const recentCampaigns = await prisma.campaign.findMany({
      where: { tenantId },
      include: { user: { select: { name: true } } },
      orderBy: { createdAt: "desc" },
      take: 5,
    })

    // Combine and format activities
    const activities = [
      ...recentPosts.map((post) => ({
        id: post.id,
        type: "post_created",
        title: `New post created`,
        description: post.title || post.content.substring(0, 50) + "...",
        timestamp: post.createdAt,
        user: post.user.name,
        metadata: { status: post.status, platforms: post.platforms },
      })),
      ...recentCampaigns.map((campaign) => ({
        id: campaign.id,
        type: "campaign_created",
        title: `New campaign created`,
        description: campaign.name,
        timestamp: campaign.createdAt,
        user: campaign.user.name,
        metadata: { status: campaign.status, type: campaign.campaignType },
      })),
    ]

    // Sort by timestamp and take recent 20
    const sortedActivities = activities.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime()).slice(0, 20)

    return NextResponse.json({ activities: sortedActivities })
  } catch (error) {
    console.error("Failed to fetch recent activity:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

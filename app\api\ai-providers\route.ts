import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { encrypt } from "@/lib/encryption"
import { z } from "zod"

const createProviderSchema = z.object({
  name: z.string().min(1).max(100),
  providerType: z.enum(["openai", "anthropic", "google", "mistral", "huggingface", "deepseek"]),
  apiKey: z.string().min(1),
  baseUrl: z.string().url().optional(),
  models: z.array(z.string()).min(1),
  settings: z.record(z.any()).default({}),
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.tenantId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const providers = await prisma.aiProvider.findMany({
      where: { tenantId: session.user.tenantId },
      select: {
        id: true,
        name: true,
        providerType: true,
        models: true,
        settings: true,
        isActive: true,
        healthStatus: true,
        lastHealthCheck: true,
        createdAt: true,
        updatedAt: true,
      },
      orderBy: { createdAt: "desc" },
    })

    // Test health of each provider
    const providersWithHealth = await Promise.all(
      providers.map(async (provider) => {
        const healthStatus = await testProviderHealth(provider.id, provider.providerType)

        // Update health status in database
        if (healthStatus !== provider.healthStatus) {
          await prisma.aiProvider.update({
            where: { id: provider.id },
            data: {
              healthStatus,
              lastHealthCheck: new Date(),
            },
          })
        }

        return {
          ...provider,
          healthStatus,
          lastHealthCheck: new Date(),
        }
      }),
    )

    return NextResponse.json({ providers: providersWithHealth })
  } catch (error) {
    console.error("Failed to fetch AI providers:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.tenantId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = createProviderSchema.parse(body)

    // Test the API key before saving
    const isValid = await testApiKey(validatedData.providerType, validatedData.apiKey)
    if (!isValid) {
      return NextResponse.json({ error: "Invalid API key" }, { status: 400 })
    }

    // Get available models for the provider
    const availableModels = await getAvailableModels(validatedData.providerType, validatedData.apiKey)

    const provider = await prisma.aiProvider.create({
      data: {
        ...validatedData,
        tenantId: session.user.tenantId,
        apiKeyEncrypted: encrypt(validatedData.apiKey),
        models: availableModels.length > 0 ? availableModels : validatedData.models,
        healthStatus: "healthy",
        lastHealthCheck: new Date(),
      },
      select: {
        id: true,
        name: true,
        providerType: true,
        models: true,
        settings: true,
        isActive: true,
        healthStatus: true,
        lastHealthCheck: true,
        createdAt: true,
        updatedAt: true,
      },
    })

    return NextResponse.json(provider, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: "Validation error", details: error.errors }, { status: 400 })
    }

    console.error("Failed to create AI provider:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

async function testProviderHealth(providerId: string, providerType: string): Promise<string> {
  try {
    const provider = await prisma.aiProvider.findUnique({
      where: { id: providerId },
    })

    if (!provider) return "unknown"

    const isHealthy = await testApiKey(providerType, decrypt(provider.apiKeyEncrypted))
    return isHealthy ? "healthy" : "unhealthy"
  } catch (error) {
    return "unhealthy"
  }
}

async function testApiKey(providerType: string, apiKey: string): Promise<boolean> {
  try {
    switch (providerType) {
      case "openai":
        const openaiResponse = await fetch("https://api.openai.com/v1/models", {
          headers: { Authorization: `Bearer ${apiKey}` },
        })
        return openaiResponse.ok

      case "anthropic":
        const anthropicResponse = await fetch("https://api.anthropic.com/v1/messages", {
          method: "POST",
          headers: {
            "x-api-key": apiKey,
            "Content-Type": "application/json",
            "anthropic-version": "2023-06-01",
          },
          body: JSON.stringify({
            model: "claude-3-haiku-20240307",
            max_tokens: 1,
            messages: [{ role: "user", content: "test" }],
          }),
        })
        return anthropicResponse.status !== 401

      case "google":
        const googleResponse = await fetch(`https://generativelanguage.googleapis.com/v1beta/models?key=${apiKey}`)
        return googleResponse.ok

      case "mistral":
        const mistralResponse = await fetch("https://api.mistral.ai/v1/models", {
          headers: { Authorization: `Bearer ${apiKey}` },
        })
        return mistralResponse.ok

      default:
        return false
    }
  } catch (error) {
    return false
  }
}

async function getAvailableModels(providerType: string, apiKey: string): Promise<string[]> {
  try {
    switch (providerType) {
      case "openai":
        const openaiResponse = await fetch("https://api.openai.com/v1/models", {
          headers: { Authorization: `Bearer ${apiKey}` },
        })
        if (openaiResponse.ok) {
          const data = await openaiResponse.json()
          return data.data.filter((model: any) => model.id.includes("gpt")).map((model: any) => model.id)
        }
        break

      case "anthropic":
        return ["claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307"]

      case "google":
        const googleResponse = await fetch(`https://generativelanguage.googleapis.com/v1beta/models?key=${apiKey}`)
        if (googleResponse.ok) {
          const data = await googleResponse.json()
          return data.models
            .filter((model: any) => model.name.includes("gemini"))
            .map((model: any) => model.name.split("/").pop())
        }
        break

      case "mistral":
        const mistralResponse = await fetch("https://api.mistral.ai/v1/models", {
          headers: { Authorization: `Bearer ${apiKey}` },
        })
        if (mistralResponse.ok) {
          const data = await mistralResponse.json()
          return data.data.map((model: any) => model.id)
        }
        break
    }

    return []
  } catch (error) {
    console.error(`Failed to fetch models for ${providerType}:`, error)
    return []
  }
}

// Import decrypt function
import { decrypt } from "@/lib/encryption"

import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.tenantId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const period = searchParams.get("period") || "30d"

    const now = new Date()
    const daysBack = period === "7d" ? 7 : period === "90d" ? 90 : 30
    const startDate = new Date(now.getTime() - daysBack * 24 * 60 * 60 * 1000)

    const tenantId = session.user.tenantId

    // Get posts with their analytics
    const postsWithAnalytics = await prisma.post.findMany({
      where: {
        tenantId,
        status: "published",
        publishedAt: { gte: startDate },
      },
      include: {
        analyticsEvents: {
          where: {
            createdAt: { gte: startDate },
          },
        },
      },
      orderBy: { publishedAt: "desc" },
    })

    // Calculate engagement metrics for each post
    const posts = postsWithAnalytics.map((post) => {
      const analytics = post.analyticsEvents.reduce(
        (acc, event) => {
          acc[event.eventType] = (acc[event.eventType] || 0) + event.value
          return acc
        },
        {} as Record<string, number>,
      )

      const impressions = analytics.impression || 0
      const engagement =
        (analytics.like || 0) + (analytics.comment || 0) + (analytics.share || 0) + (analytics.reaction || 0)
      const engagementRate = impressions > 0 ? (engagement / impressions) * 100 : 0

      // Determine primary platform (first in platforms array)
      const platform = post.platforms[0] || "unknown"

      return {
        id: post.id,
        title: post.title || post.content.substring(0, 50) + "...",
        platform,
        engagement,
        impressions,
        type: "post",
        engagementRate,
      }
    })

    // Sort by engagement and take top performers
    const topPosts = posts.sort((a, b) => b.engagement - a.engagement).slice(0, 10)

    return NextResponse.json({ posts: topPosts })
  } catch (error) {
    console.error("Failed to fetch top posts:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

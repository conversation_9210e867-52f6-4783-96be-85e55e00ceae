import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const tenantId = session.user.tenantId

    // Get posts with their engagement metrics
    const posts = await prisma.post.findMany({
      where: {
        tenantId,
        status: "published",
        publishedAt: { not: null },
      },
      include: {
        analyticsEvents: {
          where: {
            eventType: { in: ["like", "comment", "share", "reaction"] },
          },
        },
      },
      orderBy: { publishedAt: "desc" },
      take: 100,
    })

    // Calculate engagement for each post
    const postsWithEngagement = posts.map((post) => {
      const totalEngagement = post.analyticsEvents.reduce((sum, event) => sum + event.value, 0)
      const reach = (post.metrics as any)?.reach || 0

      return {
        id: post.id,
        title: post.title || "Untitled",
        content: post.content.substring(0, 100) + (post.content.length > 100 ? "..." : ""),
        platform: post.platforms[0] || "unknown",
        engagement: totalEngagement,
        reach,
        publishedAt: post.publishedAt!,
        metrics: post.metrics,
      }
    })

    // Sort by engagement and take top 10
    const topPosts = postsWithEngagement.sort((a, b) => b.engagement - a.engagement).slice(0, 10)

    return NextResponse.json({ posts: topPosts })
  } catch (error) {
    console.error("Failed to fetch top posts:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

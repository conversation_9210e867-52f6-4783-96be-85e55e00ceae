import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.tenantId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Verify account belongs to tenant
    const existingAccount = await prisma.socialAccount.findFirst({
      where: {
        id: params.id,
        tenantId: session.user.tenantId,
      },
    })

    if (!existingAccount) {
      return NextResponse.json({ error: "Account not found" }, { status: 404 })
    }

    await prisma.socialAccount.delete({
      where: { id: params.id },
    })

    return NextResponse.json({ message: "Account disconnected successfully" })
  } catch (error) {
    console.error("Failed to disconnect social account:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

import { prisma } from "./prisma"
import { SocialMediaManager } from "./social-media"
import { createNotification } from "./notifications"

export interface JobPayload {
  [key: string]: any
}

export interface Job {
  id: string
  tenantId: string
  type: string
  payload: JobPayload
  status: string
  attempts: number
  maxAttempts: number
  scheduledFor: Date
  processedAt?: Date
  error?: string
}

export class JobQueue {
  private static instance: JobQueue
  private processing = false
  private intervalId?: NodeJS.Timeout

  private constructor() {}

  static getInstance(): JobQueue {
    if (!JobQueue.instance) {
      JobQueue.instance = new JobQueue()
    }
    return JobQueue.instance
  }

  async start() {
    if (this.processing) return

    this.processing = true
    console.log("Job queue started")

    // Process jobs every 30 seconds
    this.intervalId = setInterval(() => {
      this.processJobs()
    }, 30000)

    // Process immediately on start
    this.processJobs()
  }

  stop() {
    if (this.intervalId) {
      clearInterval(this.intervalId)
      this.intervalId = undefined
    }
    this.processing = false
    console.log("Job queue stopped")
  }

  async addJob(
    tenantId: string,
    type: string,
    payload: JobPayload,
    scheduledFor: Date = new Date(),
    maxAttempts = 3,
  ): Promise<string> {
    const job = await prisma.jobQueue.create({
      data: {
        tenantId,
        type,
        payload,
        scheduledFor,
        maxAttempts,
        status: "pending",
      },
    })

    console.log(`Job ${job.id} added: ${type}`)
    return job.id
  }

  private async processJobs() {
    try {
      // Get pending jobs that are due
      const jobs = await prisma.jobQueue.findMany({
        where: {
          status: "pending",
          scheduledFor: { lte: new Date() },
          attempts: { lt: prisma.jobQueue.fields.maxAttempts },
        },
        orderBy: { scheduledFor: "asc" },
        take: 10, // Process up to 10 jobs at a time
      })

      if (jobs.length === 0) return

      console.log(`Processing ${jobs.length} jobs`)

      for (const job of jobs) {
        await this.processJob(job)
      }
    } catch (error) {
      console.error("Error processing jobs:", error)
    }
  }

  private async processJob(job: Job) {
    try {
      // Mark job as processing
      await prisma.jobQueue.update({
        where: { id: job.id },
        data: {
          status: "processing",
          attempts: job.attempts + 1,
        },
      })

      console.log(`Processing job ${job.id}: ${job.type}`)

      // Process based on job type
      switch (job.type) {
        case "publish_post":
          await this.processPublishPost(job)
          break
        case "refresh_social_token":
          await this.processRefreshToken(job)
          break
        case "sync_analytics":
          await this.processSyncAnalytics(job)
          break
        case "send_notification":
          await this.processSendNotification(job)
          break
        default:
          throw new Error(`Unknown job type: ${job.type}`)
      }

      // Mark job as completed
      await prisma.jobQueue.update({
        where: { id: job.id },
        data: {
          status: "completed",
          processedAt: new Date(),
          error: null,
        },
      })

      console.log(`Job ${job.id} completed successfully`)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error"
      console.error(`Job ${job.id} failed:`, errorMessage)

      // Check if we should retry
      if (job.attempts + 1 >= job.maxAttempts) {
        // Mark as failed
        await prisma.jobQueue.update({
          where: { id: job.id },
          data: {
            status: "failed",
            error: errorMessage,
            processedAt: new Date(),
          },
        })

        // Create notification for failed job
        await createNotification(
          job.tenantId,
          "job_failed",
          "Job Failed",
          `Job ${job.type} failed after ${job.maxAttempts} attempts: ${errorMessage}`,
          { jobId: job.id, jobType: job.type },
        )
      } else {
        // Schedule retry with exponential backoff
        const retryDelay = Math.pow(2, job.attempts) * 60000 // 1min, 2min, 4min, etc.
        const nextAttempt = new Date(Date.now() + retryDelay)

        await prisma.jobQueue.update({
          where: { id: job.id },
          data: {
            status: "pending",
            scheduledFor: nextAttempt,
            error: errorMessage,
          },
        })

        console.log(`Job ${job.id} scheduled for retry at ${nextAttempt}`)
      }
    }
  }

  private async processPublishPost(job: Job) {
    const { postId } = job.payload

    const post = await prisma.post.findFirst({
      where: { id: postId, tenantId: job.tenantId },
    })

    if (!post) {
      throw new Error(`Post ${postId} not found`)
    }

    // Get social accounts for the platforms
    const socialAccounts = await prisma.socialAccount.findMany({
      where: {
        tenantId: job.tenantId,
        platform: { in: post.platforms },
        isActive: true,
      },
    })

    if (socialAccounts.length === 0) {
      throw new Error("No active social accounts found for specified platforms")
    }

    const socialManager = new SocialMediaManager()

    // Add providers for each account
    socialAccounts.forEach((account) => {
      socialManager.addProvider(account)
    })

    // Publish to all platforms
    const results = await socialManager.publishToMultiplePlatforms(
      socialAccounts.map((acc) => acc.id),
      {
        content: post.content,
        mediaUrls: post.mediaUrls as string[],
        hashtags: post.hashtags as string[],
      },
    )

    // Update post status based on results
    const successfulPosts = Object.values(results).filter((result) => result.success)
    const allSuccessful = successfulPosts.length === socialAccounts.length
    const anySuccessful = successfulPosts.length > 0

    await prisma.post.update({
      where: { id: postId },
      data: {
        status: allSuccessful ? "published" : anySuccessful ? "partially_published" : "failed",
        publishedAt: anySuccessful ? new Date() : null,
        metrics: {
          publishResults: results,
          successfulPlatforms: successfulPosts.length,
          totalPlatforms: socialAccounts.length,
        },
      },
    })

    // Create analytics events for successful posts
    for (const [accountId, result] of Object.entries(results)) {
      if (result.success && result.metrics) {
        const account = socialAccounts.find((acc) => acc.id === accountId)
        if (account) {
          for (const [metricType, value] of Object.entries(result.metrics)) {
            if (typeof value === "number") {
              await prisma.analyticsEvent.create({
                data: {
                  tenantId: job.tenantId,
                  postId,
                  eventType: metricType,
                  platform: account.platform,
                  value,
                  metadata: {
                    accountId,
                    postId: result.postId,
                    initialMetric: true,
                  },
                },
              })
            }
          }
        }
      }
    }

    // Create notification
    await createNotification(
      job.tenantId,
      allSuccessful ? "post_published" : anySuccessful ? "post_partially_published" : "post_failed",
      allSuccessful
        ? "Post Published Successfully"
        : anySuccessful
          ? "Post Partially Published"
          : "Post Publishing Failed",
      allSuccessful
        ? `Your post "${post.title || post.content.substring(0, 50)}..." has been published to all platforms.`
        : anySuccessful
          ? `Your post "${post.title || post.content.substring(0, 50)}..." was published to ${successfulPosts.length} of ${socialAccounts.length} platforms.`
          : `Failed to publish your post "${post.title || post.content.substring(0, 50)}..." to any platform.`,
      { postId, results },
    )
  }

  private async processRefreshToken(job: Job) {
    const { accountId } = job.payload

    const account = await prisma.socialAccount.findFirst({
      where: { id: accountId, tenantId: job.tenantId },
    })

    if (!account) {
      throw new Error(`Social account ${accountId} not found`)
    }

    const socialManager = new SocialMediaManager()
    socialManager.addProvider(account)

    // Attempt to refresh the token
    const provider = socialManager.getProvider(accountId)
    if (!provider) {
      throw new Error(`Provider not found for account ${accountId}`)
    }

    const refreshed = await provider.refreshToken()

    if (refreshed) {
      // Update health status
      await prisma.socialAccount.update({
        where: { id: accountId },
        data: {
          healthStatus: "healthy",
          lastHealthCheck: new Date(),
        },
      })

      await createNotification(
        job.tenantId,
        "token_refreshed",
        "Token Refreshed",
        `Successfully refreshed access token for ${account.platform} account: ${account.accountName}`,
        { accountId },
      )
    } else {
      // Mark account as unhealthy
      await prisma.socialAccount.update({
        where: { id: accountId },
        data: {
          healthStatus: "token_expired",
          lastHealthCheck: new Date(),
        },
      })

      await createNotification(
        job.tenantId,
        "token_refresh_failed",
        "Token Refresh Failed",
        `Failed to refresh access token for ${account.platform} account: ${account.accountName}. Please reconnect your account.`,
        { accountId },
      )

      throw new Error(`Failed to refresh token for account ${accountId}`)
    }
  }

  private async processSyncAnalytics(job: Job) {
    const { accountId, postIds } = job.payload

    const account = await prisma.socialAccount.findFirst({
      where: { id: accountId, tenantId: job.tenantId },
    })

    if (!account) {
      throw new Error(`Social account ${accountId} not found`)
    }

    const socialManager = new SocialMediaManager()
    socialManager.addProvider(account)

    const provider = socialManager.getProvider(accountId)
    if (!provider) {
      throw new Error(`Provider not found for account ${accountId}`)
    }

    // Sync analytics for each post
    for (const postId of postIds) {
      try {
        const post = await prisma.post.findFirst({
          where: { id: postId, tenantId: job.tenantId },
        })

        if (!post || !post.metrics || typeof post.metrics !== "object") {
          continue
        }

        const metrics = post.metrics as any
        const externalPostId = metrics.publishResults?.[accountId]?.postId

        if (!externalPostId) {
          continue
        }

        const analytics = await provider.getAnalytics(externalPostId)

        if (analytics) {
          // Store updated analytics
          for (const [metricType, value] of Object.entries(analytics)) {
            if (typeof value === "number") {
              await prisma.analyticsEvent.create({
                data: {
                  tenantId: job.tenantId,
                  postId,
                  eventType: metricType,
                  platform: account.platform,
                  value,
                  metadata: {
                    accountId,
                    postId: externalPostId,
                    syncedAt: new Date(),
                  },
                },
              })
            }
          }
        }
      } catch (error) {
        console.error(`Failed to sync analytics for post ${postId}:`, error)
        // Continue with other posts
      }
    }
  }

  private async processSendNotification(job: Job) {
    const { userId, type, title, message, data } = job.payload

    await createNotification(job.tenantId, type, title, message, data, userId)
  }
}

// Initialize job queue on module load
const jobQueue = JobQueue.getInstance()

// Start job queue in production
if (process.env.NODE_ENV === "production") {
  jobQueue.start()
}

export { jobQueue }

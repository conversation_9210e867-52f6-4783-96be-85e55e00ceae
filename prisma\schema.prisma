generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model Tenant {
  id               String   @id @default(uuid()) @db.Uuid
  name             String
  domain           String   @unique
  settings         Json     @default("{}")
  subscriptionTier String   @default("free") @map("subscription_tier")
  isActive         Boolean  @default(true) @map("is_active")
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @default(now()) @updatedAt @map("updated_at")

  users            User[]
  campaigns        Campaign[]
  posts            Post[]
  socialAccounts   SocialAccount[] @map("social_accounts")
  aiProviders      AIProvider[]    @map("ai_providers")
  aiUsage          AIUsage[]       @map("ai_usage")
  analyticsEvents  AnalyticsEvent[] @map("analytics_events")
  jobQueue         JobQueue[]      @map("job_queue")
  notifications    Notification[]
  teamMembers      TeamMember[]    @map("team_members")
  fileUploads      FileUpload[]    @map("file_uploads")

  @@map("tenants")
}

model User {
  id            String    @id @default(uuid()) @db.Uuid
  tenantId      String    @map("tenant_id") @db.Uuid
  email         String    @unique
  name          String
  image         String?
  password      String?
  role          String    @default("user")
  emailVerified DateTime? @map("email_verified")
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @default(now()) @updatedAt @map("updated_at")

  tenant          Tenant         @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  accounts        Account[]
  sessions        Session[]
  campaigns       Campaign[]
  posts           Post[]
  socialAccounts  SocialAccount[] @map("social_accounts")
  aiProviders     AIProvider[]    @map("ai_providers")
  aiUsage         AIUsage[]       @map("ai_usage")
  notifications   Notification[]
  teamMembers     TeamMember[]    @map("team_members")
  fileUploads     FileUpload[]    @map("file_uploads")

  @@map("users")
}

model Campaign {
  id             String    @id @default(uuid()) @db.Uuid
  tenantId       String    @map("tenant_id") @db.Uuid
  userId         String    @map("user_id") @db.Uuid
  name           String
  description    String?
  campaignType   String    @map("campaign_type")
  status         String    @default("draft")
  budget         Decimal   @default(0) @db.Decimal(10, 2)
  spent          Decimal   @default(0) @db.Decimal(10, 2)
  startDate      DateTime  @map("start_date")
  endDate        DateTime? @map("end_date")
  targetAudience Json      @default("{}") @map("target_audience")
  settings       Json      @default("{}")
  metrics        Json      @default("{}")
  createdAt      DateTime  @default(now()) @map("created_at")
  updatedAt      DateTime  @default(now()) @updatedAt @map("updated_at")

  tenant          Tenant           @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  user            User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  posts           Post[]
  analyticsEvents AnalyticsEvent[] @map("analytics_events")

  @@map("campaigns")
}

model Post {
  id           String    @id @default(uuid()) @db.Uuid
  tenantId     String    @map("tenant_id") @db.Uuid
  userId       String    @map("user_id") @db.Uuid
  campaignId   String?   @map("campaign_id") @db.Uuid
  title        String?
  content      String
  mediaUrls    String[]  @default([]) @map("media_urls")
  hashtags     String[]  @default([])
  platforms    String[]  @default([])
  status       String    @default("draft")
  scheduledAt  DateTime? @map("scheduled_at")
  publishedAt  DateTime? @map("published_at")
  aiGenerated  Boolean   @default(false) @map("ai_generated")
  aiModelUsed  String?   @map("ai_model_used")
  metrics      Json      @default("{}")
  createdAt    DateTime  @default(now()) @map("created_at")
  updatedAt    DateTime  @default(now()) @updatedAt @map("updated_at")

  tenant          Tenant           @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  user            User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  campaign        Campaign?        @relation(fields: [campaignId], references: [id], onDelete: SetNull)
  analyticsEvents AnalyticsEvent[] @map("analytics_events")

  @@map("posts")
}

model SocialAccount {
  id                     String    @id @default(uuid()) @db.Uuid
  tenantId               String    @map("tenant_id") @db.Uuid
  userId                 String    @map("user_id") @db.Uuid
  platform               String
  accountId              String    @map("account_id")
  accountName            String    @map("account_name")
  accessTokenEncrypted   String    @map("access_token_encrypted")
  refreshTokenEncrypted  String?   @map("refresh_token_encrypted")
  expiresAt              DateTime? @map("expires_at")
  permissions            String[]  @default([])
  isActive               Boolean   @default(true) @map("is_active")
  healthStatus           String    @default("healthy") @map("health_status")
  lastHealthCheck        DateTime  @default(now()) @map("last_health_check")
  createdAt              DateTime  @default(now()) @map("created_at")
  updatedAt              DateTime  @default(now()) @updatedAt @map("updated_at")

  tenant Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([tenantId, platform, accountId])
  @@map("social_accounts")
}

model AIProvider {
  id              String    @id @default(uuid()) @db.Uuid
  tenantId        String    @map("tenant_id") @db.Uuid
  userId          String    @map("user_id") @db.Uuid
  name            String
  providerType    String    @map("provider_type")
  apiKeyEncrypted String    @map("api_key_encrypted")
  baseUrl         String?   @map("base_url")
  models          String[]  @default([])
  isActive        Boolean   @default(true) @map("is_active")
  healthStatus    String    @default("healthy") @map("health_status")
  lastHealthCheck DateTime? @map("last_health_check")
  settings        Json      @default("{}")
  createdAt       DateTime  @default(now()) @map("created_at")
  updatedAt       DateTime  @default(now()) @updatedAt @map("updated_at")

  tenant Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("ai_providers")
}

model AIUsage {
  id          String   @id @default(uuid()) @db.Uuid
  tenantId    String   @map("tenant_id") @db.Uuid
  userId      String   @map("user_id") @db.Uuid
  provider    String
  model       String
  tokensUsed  Int      @map("tokens_used")
  cost        Decimal  @db.Decimal(10, 4)
  requestType String   @map("request_type")
  createdAt   DateTime @default(now()) @map("created_at")

  tenant Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("ai_usage")
}

model AnalyticsEvent {
  id         String   @id @default(uuid()) @db.Uuid
  tenantId   String   @map("tenant_id") @db.Uuid
  postId     String?  @map("post_id") @db.Uuid
  campaignId String?  @map("campaign_id") @db.Uuid
  eventType  String   @map("event_type")
  platform   String
  value      Int      @default(0)
  metadata   Json     @default("{}")
  createdAt  DateTime @default(now()) @map("created_at")

  tenant   Tenant    @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  post     Post?     @relation(fields: [postId], references: [id], onDelete: Cascade)
  campaign Campaign? @relation(fields: [campaignId], references: [id], onDelete: Cascade)

  @@map("analytics_events")
}

model JobQueue {
  id           String    @id @default(uuid()) @db.Uuid
  tenantId     String    @map("tenant_id") @db.Uuid
  type         String
  payload      Json
  status       String    @default("pending")
  attempts     Int       @default(0)
  maxAttempts  Int       @default(3) @map("max_attempts")
  scheduledFor DateTime  @default(now()) @map("scheduled_for")
  processedAt  DateTime? @map("processed_at")
  error        String?
  createdAt    DateTime  @default(now()) @map("created_at")
  updatedAt    DateTime  @default(now()) @updatedAt @map("updated_at")

  tenant Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@map("job_queue")
}

model Notification {
  id        String   @id @default(uuid()) @db.Uuid
  tenantId  String   @map("tenant_id") @db.Uuid
  userId    String   @map("user_id") @db.Uuid
  type      String
  title     String
  message   String
  data      Json     @default("{}")
  read      Boolean  @default(false)
  createdAt DateTime @default(now()) @map("created_at")

  tenant Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notifications")
}

model TeamMember {
  id          String   @id @default(uuid()) @db.Uuid
  tenantId    String   @map("tenant_id") @db.Uuid
  userId      String   @map("user_id") @db.Uuid
  role        String
  permissions String[] @default([])
  invitedBy   String?  @map("invited_by") @db.Uuid
  joinedAt    DateTime @default(now()) @map("joined_at")
  createdAt   DateTime @default(now()) @map("created_at")

  tenant Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([tenantId, userId])
  @@map("team_members")
}

model FileUpload {
  id           String   @id @default(uuid()) @db.Uuid
  tenantId     String   @map("tenant_id") @db.Uuid
  userId       String   @map("user_id") @db.Uuid
  filename     String
  originalName String   @map("original_name")
  mimeType     String   @map("mime_type")
  size         Int
  url          String
  metadata     Json     @default("{}")
  createdAt    DateTime @default(now()) @map("created_at")

  tenant Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("file_uploads")
}

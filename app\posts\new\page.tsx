"use client"

import type React from "react"

import { useState, useEffect, useRef } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  ArrowLeft,
  Upload,
  X,
  CalendarIcon,
  Wand2,
  Save,
  Send,
  ImageIcon,
  Video,
  Facebook,
  Twitter,
  Linkedin,
  Instagram,
} from "lucide-react"
import { format } from "date-fns"
import { cn } from "@/lib/utils"
import { useToast } from "@/hooks/use-toast"
import type { Campaign, AIProvider } from "@/lib/types"

const platformIcons = {
  facebook: Facebook,
  twitter: Twitter,
  linkedin: Linkedin,
  instagram: Instagram,
}

export default function NewPostPage() {
  const router = useRouter()
  const { toast } = useToast()
  const fileInputRef = useRef<HTMLInputElement>(null)

  const [loading, setLoading] = useState(false)
  const [campaigns, setCampaigns] = useState<Campaign[]>([])
  const [aiProviders, setAiProviders] = useState<AIProvider[]>([])
  const [aiDialogOpen, setAiDialogOpen] = useState(false)
  const [generatingContent, setGeneratingContent] = useState(false)

  const [formData, setFormData] = useState({
    campaignId: "", // Updated to non-empty string
    title: "",
    content: "",
    mediaUrls: [] as string[],
    hashtags: [] as string[],
    platforms: [] as string[],
    scheduledAt: undefined as Date | undefined,
    aiGenerated: false,
    aiModelUsed: "",
  })

  const [mediaFiles, setMediaFiles] = useState<File[]>([])
  const [currentHashtag, setCurrentHashtag] = useState("")
  const [aiSettings, setAiSettings] = useState({
    providerId: "", // Updated to non-empty string
    model: "", // Updated to non-empty string
    contentType: "post" as "post" | "caption" | "hashtags" | "title",
    tone: "friendly" as "professional" | "casual" | "friendly" | "formal" | "creative",
    platform: "", // Updated to non-empty string
    prompt: "",
  })

  useEffect(() => {
    fetchCampaigns()
    fetchAiProviders()
  }, [])

  async function fetchCampaigns() {
    try {
      const response = await fetch("/api/campaigns")
      if (response.ok) {
        const data = await response.json()
        setCampaigns(data.campaigns)
      }
    } catch (error) {
      console.error("Failed to fetch campaigns:", error)
    }
  }

  async function fetchAiProviders() {
    try {
      const response = await fetch("/api/ai-providers")
      if (response.ok) {
        const data = await response.json()
        setAiProviders(data.providers.filter((p: AIProvider) => p.isActive))
      }
    } catch (error) {
      console.error("Failed to fetch AI providers:", error)
    }
  }

  async function handleFileUpload(files: FileList | null) {
    if (!files) return

    const newFiles = Array.from(files)
    const validFiles = newFiles.filter((file) => {
      const isValidType = file.type.startsWith("image/") || file.type.startsWith("video/")
      const isValidSize = file.size <= 50 * 1024 * 1024 // 50MB limit

      if (!isValidType) {
        toast({
          title: "Invalid file type",
          description: `${file.name} is not a valid image or video file`,
          variant: "destructive",
        })
      }
      if (!isValidSize) {
        toast({
          title: "File too large",
          description: `${file.name} exceeds the 50MB limit`,
          variant: "destructive",
        })
      }

      return isValidType && isValidSize
    })

    if (validFiles.length > 0) {
      setMediaFiles((prev) => [...prev, ...validFiles])

      // Upload files and get URLs
      const uploadPromises = validFiles.map(async (file) => {
        const formData = new FormData()
        formData.append("file", file)

        try {
          const response = await fetch("/api/upload", {
            method: "POST",
            body: formData,
          })

          if (response.ok) {
            const data = await response.json()
            return data.url
          }
        } catch (error) {
          console.error("Upload failed:", error)
        }
        return null
      })

      const urls = await Promise.all(uploadPromises)
      const validUrls = urls.filter(Boolean) as string[]

      setFormData((prev) => ({
        ...prev,
        mediaUrls: [...prev.mediaUrls, ...validUrls],
      }))
    }
  }

  function removeMedia(index: number) {
    setMediaFiles((prev) => prev.filter((_, i) => i !== index))
    setFormData((prev) => ({
      ...prev,
      mediaUrls: prev.mediaUrls.filter((_, i) => i !== index),
    }))
  }

  function addHashtag() {
    if (currentHashtag.trim() && !formData.hashtags.includes(currentHashtag.trim())) {
      const hashtag = currentHashtag.startsWith("#") ? currentHashtag.trim() : `#${currentHashtag.trim()}`
      setFormData((prev) => ({
        ...prev,
        hashtags: [...prev.hashtags, hashtag],
      }))
      setCurrentHashtag("")
    }
  }

  function removeHashtag(hashtag: string) {
    setFormData((prev) => ({
      ...prev,
      hashtags: prev.hashtags.filter((h) => h !== hashtag),
    }))
  }

  function togglePlatform(platform: string) {
    setFormData((prev) => ({
      ...prev,
      platforms: prev.platforms.includes(platform)
        ? prev.platforms.filter((p) => p !== platform)
        : [...prev.platforms, platform],
    }))
  }

  async function generateAiContent() {
    if (!aiSettings.providerId || !aiSettings.model || !aiSettings.prompt) {
      toast({
        title: "Missing Information",
        description: "Please select a provider, model, and enter a prompt",
        variant: "destructive",
      })
      return
    }

    setGeneratingContent(true)
    try {
      const response = await fetch("/api/ai/generate", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(aiSettings),
      })

      if (response.ok) {
        const data = await response.json()

        if (aiSettings.contentType === "hashtags") {
          const hashtags = data.content.split(" ").filter((h: string) => h.startsWith("#"))
          setFormData((prev) => ({
            ...prev,
            hashtags: [...new Set([...prev.hashtags, ...hashtags])],
            aiGenerated: true,
            aiModelUsed: aiSettings.model,
          }))
        } else {
          setFormData((prev) => ({
            ...prev,
            [aiSettings.contentType === "title" ? "title" : "content"]: data.content,
            aiGenerated: true,
            aiModelUsed: aiSettings.model,
          }))
        }

        setAiDialogOpen(false)
        toast({
          title: "Content Generated",
          description: "AI has successfully generated your content",
        })
      } else {
        const error = await response.json()
        toast({
          title: "Generation Failed",
          description: error.error || "Failed to generate content",
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      })
    } finally {
      setGeneratingContent(false)
    }
  }

  async function handleSubmit(e: React.FormEvent, action: "draft" | "schedule" | "publish") {
    e.preventDefault()
    setLoading(true)

    try {
      const response = await fetch("/api/posts", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          ...formData,
          scheduledAt: action === "schedule" ? formData.scheduledAt?.toISOString() : undefined,
          status: action === "draft" ? "draft" : action === "schedule" ? "scheduled" : "published",
        }),
      })

      if (response.ok) {
        const post = await response.json()
        toast({
          title: "Success",
          description: `Post ${action === "draft" ? "saved as draft" : action === "schedule" ? "scheduled" : "published"} successfully`,
        })
        router.push(`/posts/${post.id}`)
      } else {
        const error = await response.json()
        toast({
          title: "Error",
          description: error.error || "Failed to save post",
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const availablePlatforms = [
    { id: "facebook", name: "Facebook", icon: Facebook },
    { id: "twitter", name: "Twitter/X", icon: Twitter },
    { id: "linkedin", name: "LinkedIn", icon: Linkedin },
    { id: "instagram", name: "Instagram", icon: Instagram },
  ]

  const selectedProvider = aiProviders.find((p) => p.id === aiSettings.providerId)

  return (
    <div className="flex-1 space-y-6 p-4 md:p-8 pt-6">
      <div className="flex items-center space-x-4">
        <Button variant="ghost" size="sm" onClick={() => router.back()}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Create New Post</h2>
          <p className="text-muted-foreground">Create and schedule content across multiple platforms</p>
        </div>
      </div>

      <form className="space-y-6">
        <div className="grid gap-6 lg:grid-cols-3">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Campaign Selection */}
            <Card>
              <CardHeader>
                <CardTitle>Campaign & Basic Info</CardTitle>
                <CardDescription>Select the campaign and add basic post information</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="campaign">Campaign *</Label>
                    <Select
                      value={formData.campaignId}
                      onValueChange={(value) => setFormData({ ...formData, campaignId: value })}
                      required
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select campaign" />
                      </SelectTrigger>
                      <SelectContent>
                        {campaigns.map((campaign) => (
                          <SelectItem key={campaign.id} value={campaign.id}>
                            {campaign.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="title">Post Title</Label>
                    <Input
                      id="title"
                      value={formData.title}
                      onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                      placeholder="Optional post title"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Content Creation */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Content</CardTitle>
                    <CardDescription>Write your post content or generate it with AI</CardDescription>
                  </div>
                  <Dialog open={aiDialogOpen} onOpenChange={setAiDialogOpen}>
                    <DialogTrigger asChild>
                      <Button variant="outline" size="sm">
                        <Wand2 className="mr-2 h-4 w-4" />
                        AI Generate
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="sm:max-w-[500px]">
                      <DialogHeader>
                        <DialogTitle>Generate Content with AI</DialogTitle>
                        <DialogDescription>Use AI to generate engaging content for your post</DialogDescription>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div className="grid gap-4 md:grid-cols-2">
                          <div className="space-y-2">
                            <Label>AI Provider</Label>
                            <Select
                              value={aiSettings.providerId}
                              onValueChange={(value) => setAiSettings({ ...aiSettings, providerId: value })}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select provider" />
                              </SelectTrigger>
                              <SelectContent>
                                {aiProviders.map((provider) => (
                                  <SelectItem key={provider.id} value={provider.id}>
                                    {provider.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="space-y-2">
                            <Label>Model</Label>
                            <Select
                              value={aiSettings.model}
                              onValueChange={(value) => setAiSettings({ ...aiSettings, model: value })}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select model" />
                              </SelectTrigger>
                              <SelectContent>
                                {selectedProvider?.models.map((model) => (
                                  <SelectItem key={model} value={model}>
                                    {model}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                        <div className="grid gap-4 md:grid-cols-2">
                          <div className="space-y-2">
                            <Label>Content Type</Label>
                            <Select
                              value={aiSettings.contentType}
                              onValueChange={(value: any) => setAiSettings({ ...aiSettings, contentType: value })}
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="post">Full Post</SelectItem>
                                <SelectItem value="caption">Caption</SelectItem>
                                <SelectItem value="hashtags">Hashtags</SelectItem>
                                <SelectItem value="title">Title</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="space-y-2">
                            <Label>Tone</Label>
                            <Select
                              value={aiSettings.tone}
                              onValueChange={(value: any) => setAiSettings({ ...aiSettings, tone: value })}
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="professional">Professional</SelectItem>
                                <SelectItem value="casual">Casual</SelectItem>
                                <SelectItem value="friendly">Friendly</SelectItem>
                                <SelectItem value="formal">Formal</SelectItem>
                                <SelectItem value="creative">Creative</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                        <div className="space-y-2">
                          <Label>Platform (Optional)</Label>
                          <Select
                            value={aiSettings.platform}
                            onValueChange={(value) => setAiSettings({ ...aiSettings, platform: value })}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Optimize for platform" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="">Any Platform</SelectItem>
                              <SelectItem value="facebook">Facebook</SelectItem>
                              <SelectItem value="twitter">Twitter/X</SelectItem>
                              <SelectItem value="linkedin">LinkedIn</SelectItem>
                              <SelectItem value="instagram">Instagram</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="space-y-2">
                          <Label>Prompt</Label>
                          <Textarea
                            value={aiSettings.prompt}
                            onChange={(e) => setAiSettings({ ...aiSettings, prompt: e.target.value })}
                            placeholder="Describe what you want to generate..."
                            rows={3}
                          />
                        </div>
                        <Button onClick={generateAiContent} disabled={generatingContent} className="w-full">
                          {generatingContent ? (
                            <>
                              <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-background border-t-transparent" />
                              Generating...
                            </>
                          ) : (
                            <>
                              <Wand2 className="mr-2 h-4 w-4" />
                              Generate Content
                            </>
                          )}
                        </Button>
                      </div>
                    </DialogContent>
                  </Dialog>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Label htmlFor="content">Post Content *</Label>
                  <Textarea
                    id="content"
                    value={formData.content}
                    onChange={(e) => setFormData({ ...formData, content: e.target.value })}
                    placeholder="Write your post content here..."
                    rows={6}
                    required
                  />
                  <div className="flex items-center justify-between text-sm text-muted-foreground">
                    <span>{formData.content.length} characters</span>
                    {formData.aiGenerated && (
                      <Badge variant="secondary" className="text-xs">
                        AI Generated with {formData.aiModelUsed}
                      </Badge>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Media Upload */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <ImageIcon className="h-5 w-5" /> {/* Updated to ImageIcon */}
                  <span>Media</span>
                </CardTitle>
                <CardDescription>Add images, videos, or other media to your post</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div
                    className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center hover:border-muted-foreground/50 transition-colors cursor-pointer"
                    onClick={() => fileInputRef.current?.click()}
                  >
                    <Upload className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
                    <p className="text-sm text-muted-foreground mb-1">Click to upload or drag and drop</p>
                    <p className="text-xs text-muted-foreground">Images and videos up to 50MB</p>
                  </div>
                  <input
                    ref={fileInputRef}
                    type="file"
                    multiple
                    accept="image/*,video/*"
                    onChange={(e) => handleFileUpload(e.target.files)}
                    className="hidden"
                  />

                  {mediaFiles.length > 0 && (
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                      {mediaFiles.map((file, index) => (
                        <div key={index} className="relative group">
                          <div className="aspect-square rounded-lg overflow-hidden bg-muted">
                            {file.type.startsWith("image/") ? (
                              <img
                                src={URL.createObjectURL(file) || "/placeholder.svg"}
                                alt={file.name}
                                className="w-full h-full object-cover"
                              />
                            ) : (
                              <div className="w-full h-full flex items-center justify-center">
                                <Video className="h-8 w-8 text-muted-foreground" />
                              </div>
                            )}
                          </div>
                          <Button
                            size="sm"
                            variant="destructive"
                            className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
                            onClick={() => removeMedia(index)}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                          <p className="text-xs text-muted-foreground mt-1 truncate">{file.name}</p>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Hashtags */}
            <Card>
              <CardHeader>
                <CardTitle>Hashtags</CardTitle>
                <CardDescription>Add relevant hashtags to increase discoverability</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex space-x-2">
                    <Input
                      value={currentHashtag}
                      onChange={(e) => setCurrentHashtag(e.target.value)}
                      placeholder="Add hashtag (without #)"
                      onKeyPress={(e) => e.key === "Enter" && (e.preventDefault(), addHashtag())}
                    />
                    <Button type="button" onClick={addHashtag} variant="outline">
                      Add
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {formData.hashtags.map((hashtag) => (
                      <Badge
                        key={hashtag}
                        variant="secondary"
                        className="cursor-pointer hover:bg-destructive hover:text-destructive-foreground"
                        onClick={() => removeHashtag(hashtag)}
                      >
                        {hashtag} ×
                      </Badge>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Platform Selection */}
            <Card>
              <CardHeader>
                <CardTitle>Platforms</CardTitle>
                <CardDescription>Select where to publish this post</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {availablePlatforms.map((platform) => {
                    const Icon = platform.icon
                    return (
                      <div key={platform.id} className="flex items-center space-x-2">
                        <Checkbox
                          id={platform.id}
                          checked={formData.platforms.includes(platform.id)}
                          onCheckedChange={() => togglePlatform(platform.id)}
                        />
                        <Label htmlFor={platform.id} className="flex items-center space-x-2 cursor-pointer">
                          <Icon className="h-4 w-4" />
                          <span>{platform.name}</span>
                        </Label>
                      </div>
                    )
                  })}
                </div>
              </CardContent>
            </Card>

            {/* Scheduling */}
            <Card>
              <CardHeader>
                <CardTitle>Schedule</CardTitle>
                <CardDescription>Choose when to publish this post</CardDescription>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="now" className="w-full">
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="now">Publish Now</TabsTrigger>
                    <TabsTrigger value="schedule">Schedule</TabsTrigger>
                  </TabsList>
                  <TabsContent value="now" className="space-y-2">
                    <p className="text-sm text-muted-foreground">
                      Post will be published immediately to selected platforms
                    </p>
                  </TabsContent>
                  <TabsContent value="schedule" className="space-y-4">
                    <div className="space-y-2">
                      <Label>Schedule Date & Time</Label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full justify-start text-left font-normal",
                              !formData.scheduledAt && "text-muted-foreground",
                            )}
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {formData.scheduledAt ? format(formData.scheduledAt, "PPP 'at' p") : "Pick a date and time"}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                          <Calendar
                            mode="single"
                            selected={formData.scheduledAt}
                            onSelect={(date) => setFormData({ ...formData, scheduledAt: date })}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>

            {/* Post Preview */}
            <Card>
              <CardHeader>
                <CardTitle>Preview</CardTitle>
                <CardDescription>How your post will look</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {formData.title && <h4 className="font-medium">{formData.title}</h4>}
                  {formData.content && (
                    <p className="text-sm text-muted-foreground whitespace-pre-wrap">{formData.content}</p>
                  )}
                  {formData.hashtags.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      {formData.hashtags.map((hashtag) => (
                        <span key={hashtag} className="text-xs text-blue-600">
                          {hashtag}
                        </span>
                      ))}
                    </div>
                  )}
                  {mediaFiles.length > 0 && (
                    <div className="text-xs text-muted-foreground">
                      📎 {mediaFiles.length} media file{mediaFiles.length > 1 ? "s" : ""} attached
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center justify-end space-x-4 pt-6 border-t">
          <Button type="button" variant="outline" onClick={() => router.back()}>
            Cancel
          </Button>
          <Button type="button" variant="outline" onClick={(e) => handleSubmit(e, "draft")} disabled={loading}>
            <Save className="mr-2 h-4 w-4" />
            Save Draft
          </Button>
          {formData.scheduledAt && (
            <Button
              type="button"
              onClick={(e) => handleSubmit(e, "schedule")}
              disabled={loading || formData.platforms.length === 0}
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              Schedule Post
            </Button>
          )}
          <Button
            type="button"
            onClick={(e) => handleSubmit(e, "publish")}
            disabled={loading || formData.platforms.length === 0}
          >
            {loading ? (
              <>
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-background border-t-transparent" />
                Publishing...
              </>
            ) : (
              <>
                <Send className="mr-2 h-4 w-4" />
                Publish Now
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  )
}

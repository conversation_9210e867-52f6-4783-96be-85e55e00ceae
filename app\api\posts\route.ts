import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { SocialMediaManager } from "@/lib/social-media"
import { z } from "zod"

const createPostSchema = z.object({
  campaignId: z.string().uuid(),
  title: z.string().optional(),
  content: z.string().min(1),
  mediaUrls: z.array(z.string().url()).default([]),
  hashtags: z.array(z.string()).default([]),
  platforms: z.array(z.string()).min(1),
  scheduledAt: z.string().datetime().optional(),
  aiGenerated: z.boolean().default(false),
  aiModelUsed: z.string().optional(),
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.tenantId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = Number.parseInt(searchParams.get("page") || "1")
    const limit = Number.parseInt(searchParams.get("limit") || "10")
    const campaignId = searchParams.get("campaignId")
    const status = searchParams.get("status")

    const where = {
      tenantId: session.user.tenantId,
      ...(campaignId && { campaignId }),
      ...(status && { status }),
    }

    const [posts, total] = await Promise.all([
      prisma.post.findMany({
        where,
        include: {
          campaign: {
            select: { name: true },
          },
          user: {
            select: { firstName: true, lastName: true },
          },
        },
        orderBy: { createdAt: "desc" },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.post.count({ where }),
    ])

    return NextResponse.json({
      posts,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error("Failed to fetch posts:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.tenantId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = createPostSchema.parse(body)

    // Verify campaign belongs to tenant
    const campaign = await prisma.campaign.findFirst({
      where: {
        id: validatedData.campaignId,
        tenantId: session.user.tenantId,
      },
    })

    if (!campaign) {
      return NextResponse.json({ error: "Campaign not found" }, { status: 404 })
    }

    const post = await prisma.post.create({
      data: {
        ...validatedData,
        tenantId: session.user.tenantId,
        userId: session.user.id,
        scheduledAt: validatedData.scheduledAt ? new Date(validatedData.scheduledAt) : null,
        status: validatedData.scheduledAt ? "scheduled" : "draft",
      },
      include: {
        campaign: {
          select: { name: true },
        },
        user: {
          select: { firstName: true, lastName: true },
        },
      },
    })

    // If scheduled for immediate publishing, publish now
    if (!validatedData.scheduledAt) {
      await publishPost(post.id, session.user.tenantId)
    }

    return NextResponse.json(post, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: "Validation error", details: error.errors }, { status: 400 })
    }

    console.error("Failed to create post:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

async function publishPost(postId: string, tenantId: string) {
  try {
    const post = await prisma.post.findFirst({
      where: { id: postId, tenantId },
    })

    if (!post) return

    // Get social media accounts for the platforms
    const socialAccounts = await prisma.socialAccount.findMany({
      where: {
        tenantId,
        platform: { in: post.platforms },
        isActive: true,
      },
    })

    if (socialAccounts.length === 0) {
      await prisma.post.update({
        where: { id: postId },
        data: {
          status: "failed",
          engagementMetrics: { error: "No active social accounts found" },
        },
      })
      return
    }

    const socialManager = new SocialMediaManager()

    // Add providers for each account
    socialAccounts.forEach((account) => {
      socialManager.addProvider(account)
    })

    // Publish to all platforms
    const results = await socialManager.publishToMultiplePlatforms(
      socialAccounts.map((acc) => acc.id),
      {
        content: post.content,
        mediaUrls: post.mediaUrls as string[],
        hashtags: post.hashtags as string[],
      },
    )

    // Update post status based on results
    const successfulPosts = Object.values(results).filter((result) => result.success)
    const allSuccessful = successfulPosts.length === socialAccounts.length
    const anySuccessful = successfulPosts.length > 0

    await prisma.post.update({
      where: { id: postId },
      data: {
        status: allSuccessful ? "published" : anySuccessful ? "partially_published" : "failed",
        publishedAt: anySuccessful ? new Date() : null,
        engagementMetrics: {
          publishResults: results,
          successfulPlatforms: successfulPosts.length,
          totalPlatforms: socialAccounts.length,
        },
      },
    })

    // Create analytics events for successful posts
    for (const [accountId, result] of Object.entries(results)) {
      if (result.success && result.metrics) {
        const account = socialAccounts.find((acc) => acc.id === accountId)
        if (account) {
          for (const [metricType, value] of Object.entries(result.metrics)) {
            if (typeof value === "number") {
              await prisma.analyticsEvent.create({
                data: {
                  tenantId,
                  postId,
                  eventType: metricType,
                  platform: account.platform,
                  value,
                  metadata: {
                    accountId,
                    postId: result.postId,
                    initialMetric: true,
                  },
                },
              })
            }
          }
        }
      }
    }
  } catch (error) {
    console.error("Failed to publish post:", error)

    await prisma.post.update({
      where: { id: postId },
      data: {
        status: "failed",
        engagementMetrics: {
          error: error instanceof Error ? error.message : "Unknown error",
        },
      },
    })
  }
}

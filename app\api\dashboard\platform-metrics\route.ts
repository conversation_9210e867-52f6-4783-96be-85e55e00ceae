import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.tenantId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const period = searchParams.get("period") || "30d"

    const now = new Date()
    const daysBack = period === "7d" ? 7 : period === "90d" ? 90 : 30
    const startDate = new Date(now.getTime() - daysBack * 24 * 60 * 60 * 1000)
    const previousPeriodStart = new Date(startDate.getTime() - daysBack * 24 * 60 * 60 * 1000)

    const tenantId = session.user.tenantId

    // Get platform analytics for current period
    const currentPlatformData = await prisma.analyticsEvent.groupBy({
      by: ["platform", "eventType"],
      where: {
        tenantId,
        createdAt: { gte: startDate },
      },
      _sum: { value: true },
    })

    // Get platform analytics for previous period (for growth calculation)
    const previousPlatformData = await prisma.analyticsEvent.groupBy({
      by: ["platform", "eventType"],
      where: {
        tenantId,
        createdAt: {
          gte: previousPeriodStart,
          lt: startDate,
        },
      },
      _sum: { value: true },
    })

    // Get post counts by platform
    const postsByPlatform = await prisma.post.findMany({
      where: {
        tenantId,
        status: "published",
        publishedAt: { gte: startDate },
      },
      select: { platforms: true },
    })

    // Get connected social accounts for follower data
    const socialAccounts = await prisma.socialAccount.findMany({
      where: { tenantId, isActive: true },
      select: { platform: true, accountName: true },
    })

    // Process current period data
    const currentPlatformMap = currentPlatformData.reduce(
      (acc, item) => {
        if (!acc[item.platform]) acc[item.platform] = {}
        acc[item.platform][item.eventType] = item._sum.value || 0
        return acc
      },
      {} as Record<string, Record<string, number>>,
    )

    // Process previous period data
    const previousPlatformMap = previousPlatformData.reduce(
      (acc, item) => {
        if (!acc[item.platform]) acc[item.platform] = {}
        acc[item.platform][item.eventType] = item._sum.value || 0
        return acc
      },
      {} as Record<string, Record<string, number>>,
    )

    // Count posts by platform
    const platformPostCounts = postsByPlatform.reduce(
      (acc, post) => {
        post.platforms.forEach((platform) => {
          acc[platform] = (acc[platform] || 0) + 1
        })
        return acc
      },
      {} as Record<string, number>,
    )

    // Calculate growth
    const calculateGrowth = (current: number, previous: number) => {
      if (previous === 0) return current > 0 ? 100 : 0
      return ((current - previous) / previous) * 100
    }

    const platforms = ["facebook", "instagram", "twitter", "linkedin", "youtube", "tiktok"]

    const platformMetrics = platforms.map(async (platform) => {
      const currentData = currentPlatformMap[platform] || {}
      const previousData = previousPlatformMap[platform] || {}

      const posts = platformPostCounts[platform] || 0

      const currentEngagement =
        (currentData.like || 0) + (currentData.comment || 0) + (currentData.share || 0) + (currentData.reaction || 0)
      const previousEngagement =
        (previousData.like || 0) +
        (previousData.comment || 0) +
        (previousData.share || 0) +
        (previousData.reaction || 0)

      const engagement = currentEngagement
      const reach = currentData.reach || 0
      const impressions = currentData.impression || 0
      const clicks = currentData.click || 0

      // Calculate growth
      const engagementGrowth = calculateGrowth(currentEngagement, previousEngagement)
      const reachGrowth = calculateGrowth(currentData.reach || 0, previousData.reach || 0)
      const impressionGrowth = calculateGrowth(currentData.impression || 0, previousData.impression || 0)

      // Average growth across metrics
      const growth = (engagementGrowth + reachGrowth + impressionGrowth) / 3

      // Get follower count from social accounts (this would be updated via API calls)
      const connectedAccount = socialAccounts.find((acc) => acc.platform === platform)
      const followers = connectedAccount ? await getFollowerCount(platform, connectedAccount.accountName) : 0

      return {
        platform,
        posts,
        engagement,
        reach,
        impressions,
        clicks,
        followers,
        growth: Math.round(growth * 10) / 10, // Round to 1 decimal place
      }
    })

    const resolvedPlatformMetrics = await Promise.all(platformMetrics)

    return NextResponse.json({ platforms: resolvedPlatformMetrics })
  } catch (error) {
    console.error("Failed to fetch platform metrics:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

// Helper function to get follower count (would integrate with actual social media APIs)
async function getFollowerCount(platform: string, accountName: string): Promise<number> {
  // This would integrate with actual social media APIs
  // For now, return cached data from database or reasonable estimates

  try {
    // In production, this would make API calls to:
    // - Facebook Graph API for Facebook/Instagram
    // - Twitter API v2 for Twitter
    // - LinkedIn API for LinkedIn
    // - YouTube Data API for YouTube
    // - TikTok Business API for TikTok

    // For now, return simulated data based on platform
    const baseFollowers = {
      facebook: 2500,
      instagram: 1800,
      twitter: 950,
      linkedin: 1200,
      youtube: 850,
      tiktok: 3200,
    }

    return baseFollowers[platform as keyof typeof baseFollowers] || 0
  } catch (error) {
    console.error(`Failed to get follower count for ${platform}:`, error)
    return 0
  }
}

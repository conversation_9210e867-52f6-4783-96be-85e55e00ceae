import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const tenantId = session.user.tenantId

    // Get platform metrics from analytics events
    const platformData = await prisma.analyticsEvent.groupBy({
      by: ["platform"],
      where: { tenantId },
      _sum: {
        value: true,
      },
      _count: {
        id: true,
      },
    })

    // Get post counts by platform
    const postsByPlatform = await prisma.post.findMany({
      where: { tenantId, status: "published" },
      select: { platforms: true },
    })

    const platformPostCounts = postsByPlatform.reduce(
      (acc, post) => {
        post.platforms.forEach((platform) => {
          acc[platform] = (acc[platform] || 0) + 1
        })
        return acc
      },
      {} as Record<string, number>,
    )

    // Get engagement by platform and event type
    const engagementByPlatform = await prisma.analyticsEvent.groupBy({
      by: ["platform", "eventType"],
      where: {
        tenantId,
        eventType: { in: ["like", "comment", "share", "reaction", "reach", "impression", "click"] },
      },
      _sum: {
        value: true,
      },
    })

    const platforms = ["facebook", "instagram", "twitter", "linkedin", "youtube", "tiktok"]

    const platformMetrics = platforms.map((platform) => {
      const posts = platformPostCounts[platform] || 0

      const engagement = engagementByPlatform
        .filter((e) => e.platform === platform && ["like", "comment", "share", "reaction"].includes(e.eventType))
        .reduce((sum, e) => sum + (e._sum.value || 0), 0)

      const reach = engagementByPlatform
        .filter((e) => e.platform === platform && e.eventType === "reach")
        .reduce((sum, e) => sum + (e._sum.value || 0), 0)

      const impressions = engagementByPlatform
        .filter((e) => e.platform === platform && e.eventType === "impression")
        .reduce((sum, e) => sum + (e._sum.value || 0), 0)

      const clicks = engagementByPlatform
        .filter((e) => e.platform === platform && e.eventType === "click")
        .reduce((sum, e) => sum + (e._sum.value || 0), 0)

      // Mock followers and growth data (would come from social media APIs in production)
      const followers = Math.floor(Math.random() * 10000) + 1000
      const growth = Math.floor(Math.random() * 20) - 10

      return {
        platform,
        posts,
        engagement,
        reach,
        impressions,
        clicks,
        followers,
        growth,
      }
    })

    return NextResponse.json({ platforms: platformMetrics })
  } catch (error) {
    console.error("Failed to fetch platform metrics:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { encrypt } from "@/lib/encryption"
import { z } from "zod"

const connectAccountSchema = z.object({
  platform: z.enum(["facebook", "instagram", "twitter", "linkedin", "youtube", "tiktok"]),
  accessToken: z.string().min(1),
  refreshToken: z.string().optional(),
  expiresIn: z.number().optional(),
  accountId: z.string().min(1),
  accountName: z.string().min(1),
  permissions: z.array(z.string()).default([]),
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.tenantId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const accounts = await prisma.socialAccount.findMany({
      where: { tenantId: session.user.tenantId },
      select: {
        id: true,
        platform: true,
        accountId: true,
        accountName: true,
        permissions: true,
        isActive: true,
        expiresAt: true,
        createdAt: true,
        updatedAt: true,
      },
      orderBy: { createdAt: "desc" },
    })

    // Check token expiration and refresh if needed
    const accountsWithStatus = await Promise.all(
      accounts.map(async (account) => {
        const needsRefresh = account.expiresAt && account.expiresAt < new Date()
        let status = "active"

        if (needsRefresh) {
          status = "expired"
          // Attempt to refresh token
          try {
            const refreshed = await refreshAccountToken(account.id)
            if (refreshed) {
              status = "active"
            }
          } catch (error) {
            console.error(`Failed to refresh token for account ${account.id}:`, error)
          }
        }

        return {
          ...account,
          status,
          needsRefresh,
        }
      }),
    )

    return NextResponse.json({ accounts: accountsWithStatus })
  } catch (error) {
    console.error("Failed to fetch social accounts:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.tenantId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = connectAccountSchema.parse(body)

    // Verify the access token is valid
    const isValid = await verifyAccessToken(validatedData.platform, validatedData.accessToken)
    if (!isValid) {
      return NextResponse.json({ error: "Invalid access token" }, { status: 400 })
    }

    // Check if account already exists
    const existingAccount = await prisma.socialAccount.findFirst({
      where: {
        tenantId: session.user.tenantId,
        platform: validatedData.platform,
        accountId: validatedData.accountId,
      },
    })

    if (existingAccount) {
      // Update existing account
      const updatedAccount = await prisma.socialAccount.update({
        where: { id: existingAccount.id },
        data: {
          accessTokenEncrypted: encrypt(validatedData.accessToken),
          refreshTokenEncrypted: validatedData.refreshToken ? encrypt(validatedData.refreshToken) : null,
          expiresAt: validatedData.expiresIn ? new Date(Date.now() + validatedData.expiresIn * 1000) : null,
          accountName: validatedData.accountName,
          permissions: validatedData.permissions,
          isActive: true,
        },
        select: {
          id: true,
          platform: true,
          accountId: true,
          accountName: true,
          permissions: true,
          isActive: true,
          expiresAt: true,
          createdAt: true,
          updatedAt: true,
        },
      })

      return NextResponse.json(updatedAccount)
    } else {
      // Create new account
      const account = await prisma.socialAccount.create({
        data: {
          tenantId: session.user.tenantId,
          userId: session.user.id,
          platform: validatedData.platform,
          accountId: validatedData.accountId,
          accountName: validatedData.accountName,
          accessTokenEncrypted: encrypt(validatedData.accessToken),
          refreshTokenEncrypted: validatedData.refreshToken ? encrypt(validatedData.refreshToken) : null,
          expiresAt: validatedData.expiresIn ? new Date(Date.now() + validatedData.expiresIn * 1000) : null,
          permissions: validatedData.permissions,
        },
        select: {
          id: true,
          platform: true,
          accountId: true,
          accountName: true,
          permissions: true,
          isActive: true,
          expiresAt: true,
          createdAt: true,
          updatedAt: true,
        },
      })

      return NextResponse.json(account, { status: 201 })
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: "Validation error", details: error.errors }, { status: 400 })
    }

    console.error("Failed to connect social account:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

async function verifyAccessToken(platform: string, accessToken: string): Promise<boolean> {
  try {
    switch (platform) {
      case "facebook":
        const fbResponse = await fetch(`https://graph.facebook.com/me?access_token=${accessToken}`)
        return fbResponse.ok

      case "twitter":
        const twitterResponse = await fetch("https://api.twitter.com/2/users/me", {
          headers: { Authorization: `Bearer ${accessToken}` },
        })
        return twitterResponse.ok

      case "linkedin":
        const linkedinResponse = await fetch("https://api.linkedin.com/v2/people/~", {
          headers: { Authorization: `Bearer ${accessToken}` },
        })
        return linkedinResponse.ok

      case "instagram":
        const igResponse = await fetch(`https://graph.instagram.com/me?access_token=${accessToken}`)
        return igResponse.ok

      default:
        return false
    }
  } catch (error) {
    return false
  }
}

async function refreshAccountToken(accountId: string): Promise<boolean> {
  try {
    const account = await prisma.socialAccount.findUnique({
      where: { id: accountId },
    })

    if (!account || !account.refreshTokenEncrypted) {
      return false
    }

    // This would use the SocialMediaProvider's refreshToken method
    // Implementation depends on the specific platform
    return false
  } catch (error) {
    console.error("Failed to refresh account token:", error)
    return false
  }
}

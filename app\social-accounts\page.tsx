"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import {
  Plus,
  Facebook,
  Twitter,
  Linkedin,
  Instagram,
  Youtube,
  RefreshCw,
  Unlink,
  CheckCircle,
  XCircle,
  AlertCircle,
} from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import type { SocialAccount } from "@/lib/types"

const platformIcons = {
  facebook: Facebook,
  twitter: Twitter,
  linkedin: Linkedin,
  instagram: Instagram,
  youtube: Youtube,
  tiktok: () => <div className="h-4 w-4 bg-black rounded" />, // TikTok placeholder
}

const platformColors = {
  facebook: "bg-blue-600",
  twitter: "bg-sky-500",
  linkedin: "bg-blue-700",
  instagram: "bg-gradient-to-r from-purple-500 to-pink-500",
  youtube: "bg-red-600",
  tiktok: "bg-black",
}

export default function SocialAccountsPage() {
  const [accounts, setAccounts] = useState<(SocialAccount & { status: string })[]>([])
  const [loading, setLoading] = useState(true)
  const [connectingPlatform, setConnectingPlatform] = useState<string | null>(null)
  const { toast } = useToast()

  useEffect(() => {
    fetchAccounts()
  }, [])

  async function fetchAccounts() {
    try {
      const response = await fetch("/api/social-accounts")
      if (response.ok) {
        const data = await response.json()
        setAccounts(data.accounts)
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch social accounts",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  async function connectPlatform(platform: string) {
    setConnectingPlatform(platform)

    try {
      // Redirect to OAuth flow
      const authUrl = `/api/auth/social/${platform}`
      window.location.href = authUrl
    } catch (error) {
      toast({
        title: "Error",
        description: `Failed to connect to ${platform}`,
        variant: "destructive",
      })
      setConnectingPlatform(null)
    }
  }

  async function disconnectAccount(accountId: string) {
    try {
      const response = await fetch(`/api/social-accounts/${accountId}`, {
        method: "DELETE",
      })

      if (response.ok) {
        toast({
          title: "Success",
          description: "Account disconnected successfully",
        })
        fetchAccounts()
      } else {
        toast({
          title: "Error",
          description: "Failed to disconnect account",
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      })
    }
  }

  async function refreshAccount(accountId: string) {
    try {
      const response = await fetch(`/api/social-accounts/${accountId}/refresh`, {
        method: "POST",
      })

      if (response.ok) {
        toast({
          title: "Success",
          description: "Account refreshed successfully",
        })
        fetchAccounts()
      } else {
        toast({
          title: "Error",
          description: "Failed to refresh account",
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      })
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case "expired":
        return <AlertCircle className="h-4 w-4 text-yellow-500" />
      case "error":
        return <XCircle className="h-4 w-4 text-red-500" />
      default:
        return <AlertCircle className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800"
      case "expired":
        return "bg-yellow-100 text-yellow-800"
      case "error":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const availablePlatforms = [
    { id: "facebook", name: "Facebook", description: "Connect your Facebook pages and profiles" },
    { id: "instagram", name: "Instagram", description: "Manage your Instagram business accounts" },
    { id: "twitter", name: "Twitter/X", description: "Post and engage on Twitter/X" },
    { id: "linkedin", name: "LinkedIn", description: "Share professional content on LinkedIn" },
    { id: "youtube", name: "YouTube", description: "Upload and manage YouTube content" },
    { id: "tiktok", name: "TikTok", description: "Create and share TikTok videos" },
  ]

  const connectedPlatforms = accounts.map((acc) => acc.platform)
  const unconnectedPlatforms = availablePlatforms.filter((p) => !connectedPlatforms.includes(p.id as any))

  if (loading) {
    return (
      <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
        <div className="flex items-center justify-between">
          <h2 className="text-3xl font-bold tracking-tight">Social Accounts</h2>
        </div>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {Array.from({ length: 6 }).map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <div className="h-6 w-3/4 bg-muted animate-pulse rounded" />
                <div className="h-4 w-1/2 bg-muted animate-pulse rounded" />
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="h-4 w-full bg-muted animate-pulse rounded" />
                  <div className="h-4 w-2/3 bg-muted animate-pulse rounded" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-6 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Social Accounts</h2>
          <p className="text-muted-foreground">Connect and manage your social media accounts</p>
        </div>
      </div>

      {/* Connected Accounts */}
      {accounts.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-xl font-semibold">Connected Accounts</h3>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {accounts.map((account) => {
              const Icon = platformIcons[account.platform] || Facebook
              const platformColor = platformColors[account.platform] || "bg-gray-600"

              return (
                <Card key={account.id} className="hover:shadow-md transition-shadow">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className={`p-2 rounded-lg ${platformColor} text-white`}>
                          <Icon className="h-5 w-5" />
                        </div>
                        <div>
                          <CardTitle className="text-lg capitalize">{account.platform}</CardTitle>
                          <CardDescription>{account.accountName}</CardDescription>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(account.status)}
                        <Badge className={getStatusColor(account.status)}>{account.status}</Badge>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div>
                        <p className="text-sm font-medium mb-1">Permissions</p>
                        <div className="flex flex-wrap gap-1">
                          {account.permissions.slice(0, 3).map((permission) => (
                            <Badge key={permission} variant="secondary" className="text-xs">
                              {permission}
                            </Badge>
                          ))}
                          {account.permissions.length > 3 && (
                            <Badge variant="secondary" className="text-xs">
                              +{account.permissions.length - 3} more
                            </Badge>
                          )}
                        </div>
                      </div>

                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">Connected:</span>
                        <span>{new Date(account.createdAt).toLocaleDateString()}</span>
                      </div>

                      {account.expiresAt && (
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">Expires:</span>
                          <span className={account.status === "expired" ? "text-red-600" : ""}>
                            {new Date(account.expiresAt).toLocaleDateString()}
                          </span>
                        </div>
                      )}

                      <div className="flex items-center justify-between pt-2 border-t">
                        <div className="flex space-x-2">
                          {account.status === "expired" && (
                            <Button size="sm" variant="outline" onClick={() => refreshAccount(account.id)}>
                              <RefreshCw className="mr-2 h-4 w-4" />
                              Refresh
                            </Button>
                          )}
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button size="sm" variant="outline">
                                <Unlink className="mr-2 h-4 w-4" />
                                Disconnect
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>Disconnect Account</AlertDialogTitle>
                                <AlertDialogDescription>
                                  Are you sure you want to disconnect your {account.platform} account "
                                  {account.accountName}"? This will stop all scheduled posts to this account.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() => disconnectAccount(account.id)}
                                  className="bg-red-600 hover:bg-red-700"
                                >
                                  Disconnect
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      )}

      {/* Available Platforms */}
      {unconnectedPlatforms.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-xl font-semibold">Available Platforms</h3>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {unconnectedPlatforms.map((platform) => {
              const Icon = platformIcons[platform.id as keyof typeof platformIcons] || Facebook
              const platformColor = platformColors[platform.id as keyof typeof platformColors] || "bg-gray-600"

              return (
                <Card key={platform.id} className="hover:shadow-md transition-shadow">
                  <CardHeader>
                    <div className="flex items-center space-x-3">
                      <div className={`p-2 rounded-lg ${platformColor} text-white`}>
                        <Icon className="h-5 w-5" />
                      </div>
                      <div>
                        <CardTitle className="text-lg">{platform.name}</CardTitle>
                        <CardDescription>{platform.description}</CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <Button
                      className="w-full"
                      onClick={() => connectPlatform(platform.id)}
                      disabled={connectingPlatform === platform.id}
                    >
                      {connectingPlatform === platform.id ? (
                        <>
                          <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                          Connecting...
                        </>
                      ) : (
                        <>
                          <Plus className="mr-2 h-4 w-4" />
                          Connect {platform.name}
                        </>
                      )}
                    </Button>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      )}

      {/* Empty State */}
      {accounts.length === 0 && (
        <Card className="flex flex-col items-center justify-center py-16">
          <div className="text-center">
            <div className="flex justify-center space-x-2 mb-4">
              <Facebook className="h-8 w-8 text-blue-600" />
              <Twitter className="h-8 w-8 text-sky-500" />
              <Instagram className="h-8 w-8 text-pink-500" />
              <Linkedin className="h-8 w-8 text-blue-700" />
            </div>
            <h3 className="text-lg font-medium mb-2">No Social Accounts Connected</h3>
            <p className="text-muted-foreground mb-6">
              Connect your social media accounts to start publishing and managing your content across multiple
              platforms.
            </p>
          </div>
        </Card>
      )}
    </div>
  )
}

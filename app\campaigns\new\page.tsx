"use client"

import type React from "react"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { CalendarIcon, Target, DollarSign, Users, ArrowLeft, Save, Play } from "lucide-react"
import { format } from "date-fns"
import { cn } from "@/lib/utils"
import { useToast } from "@/hooks/use-toast"

export default function NewCampaignPage() {
  const router = useRouter()
  const { toast } = useToast()
  const [loading, setLoading] = useState(false)

  const [formData, setFormData] = useState({
    name: "",
    description: "",
    campaignType: "",
    budget: "",
    startDate: undefined as Date | undefined,
    endDate: undefined as Date | undefined,
    targetAudience: {
      demographics: {
        ageRange: "",
        gender: "",
        interests: [] as string[],
        locations: [] as string[],
      },
      platforms: [] as string[],
    },
    settings: {
      autoPublish: false,
      aiOptimization: true,
      trackConversions: true,
    },
  })

  const [currentInterest, setCurrentInterest] = useState("")
  const [currentLocation, setCurrentLocation] = useState("")

  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault()
    setLoading(true)

    try {
      const response = await fetch("/api/campaigns", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          ...formData,
          budget: formData.budget ? Number.parseFloat(formData.budget) : undefined,
          startDate: formData.startDate?.toISOString(),
          endDate: formData.endDate?.toISOString(),
        }),
      })

      if (response.ok) {
        const campaign = await response.json()
        toast({
          title: "Success",
          description: "Campaign created successfully",
        })
        router.push(`/campaigns/${campaign.id}`)
      } else {
        const error = await response.json()
        toast({
          title: "Error",
          description: error.error || "Failed to create campaign",
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  function addInterest() {
    if (currentInterest.trim() && !formData.targetAudience.demographics.interests.includes(currentInterest.trim())) {
      setFormData({
        ...formData,
        targetAudience: {
          ...formData.targetAudience,
          demographics: {
            ...formData.targetAudience.demographics,
            interests: [...formData.targetAudience.demographics.interests, currentInterest.trim()],
          },
        },
      })
      setCurrentInterest("")
    }
  }

  function removeInterest(interest: string) {
    setFormData({
      ...formData,
      targetAudience: {
        ...formData.targetAudience,
        demographics: {
          ...formData.targetAudience.demographics,
          interests: formData.targetAudience.demographics.interests.filter((i) => i !== interest),
        },
      },
    })
  }

  function addLocation() {
    if (currentLocation.trim() && !formData.targetAudience.demographics.locations.includes(currentLocation.trim())) {
      setFormData({
        ...formData,
        targetAudience: {
          ...formData.targetAudience,
          demographics: {
            ...formData.targetAudience.demographics,
            locations: [...formData.targetAudience.demographics.locations, currentLocation.trim()],
          },
        },
      })
      setCurrentLocation("")
    }
  }

  function removeLocation(location: string) {
    setFormData({
      ...formData,
      targetAudience: {
        ...formData.targetAudience,
        demographics: {
          ...formData.targetAudience.demographics,
          locations: formData.targetAudience.demographics.locations.filter((l) => l !== location),
        },
      },
    })
  }

  return (
    <div className="flex-1 space-y-6 p-4 md:p-8 pt-6">
      <div className="flex items-center space-x-4">
        <Button variant="ghost" size="sm" onClick={() => router.back()}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Create New Campaign</h2>
          <p className="text-muted-foreground">Set up your marketing campaign with AI-powered optimization</p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid gap-6 lg:grid-cols-3">
          {/* Basic Information */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Target className="h-5 w-5" />
                <span>Campaign Details</span>
              </CardTitle>
              <CardDescription>Basic information about your campaign</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="name">Campaign Name *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    placeholder="Summer Product Launch"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="campaignType">Campaign Type *</Label>
                  <Select
                    value={formData.campaignType}
                    onValueChange={(value) => setFormData({ ...formData, campaignType: value })}
                    required
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select campaign type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="social_media">Social Media</SelectItem>
                      <SelectItem value="product_launch">Product Launch</SelectItem>
                      <SelectItem value="brand_awareness">Brand Awareness</SelectItem>
                      <SelectItem value="lead_generation">Lead Generation</SelectItem>
                      <SelectItem value="engagement">Engagement</SelectItem>
                      <SelectItem value="conversion">Conversion</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  placeholder="Describe your campaign goals and strategy..."
                  rows={3}
                />
              </div>

              <div className="grid gap-4 md:grid-cols-3">
                <div className="space-y-2">
                  <Label htmlFor="budget">Budget ($)</Label>
                  <div className="relative">
                    <DollarSign className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="budget"
                      type="number"
                      value={formData.budget}
                      onChange={(e) => setFormData({ ...formData, budget: e.target.value })}
                      placeholder="5000"
                      className="pl-10"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>Start Date</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full justify-start text-left font-normal",
                          !formData.startDate && "text-muted-foreground",
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {formData.startDate ? format(formData.startDate, "PPP") : "Pick a date"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={formData.startDate}
                        onSelect={(date) => setFormData({ ...formData, startDate: date })}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>
                <div className="space-y-2">
                  <Label>End Date</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full justify-start text-left font-normal",
                          !formData.endDate && "text-muted-foreground",
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {formData.endDate ? format(formData.endDate, "PPP") : "Pick a date"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={formData.endDate}
                        onSelect={(date) => setFormData({ ...formData, endDate: date })}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Campaign Settings */}
          <Card>
            <CardHeader>
              <CardTitle>Settings</CardTitle>
              <CardDescription>Configure campaign behavior</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="autoPublish"
                  checked={formData.settings.autoPublish}
                  onCheckedChange={(checked) =>
                    setFormData({
                      ...formData,
                      settings: { ...formData.settings, autoPublish: checked as boolean },
                    })
                  }
                />
                <Label htmlFor="autoPublish" className="text-sm">
                  Auto-publish posts
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="aiOptimization"
                  checked={formData.settings.aiOptimization}
                  onCheckedChange={(checked) =>
                    setFormData({
                      ...formData,
                      settings: { ...formData.settings, aiOptimization: checked as boolean },
                    })
                  }
                />
                <Label htmlFor="aiOptimization" className="text-sm">
                  AI optimization
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="trackConversions"
                  checked={formData.settings.trackConversions}
                  onCheckedChange={(checked) =>
                    setFormData({
                      ...formData,
                      settings: { ...formData.settings, trackConversions: checked as boolean },
                    })
                  }
                />
                <Label htmlFor="trackConversions" className="text-sm">
                  Track conversions
                </Label>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Target Audience */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Users className="h-5 w-5" />
              <span>Target Audience</span>
            </CardTitle>
            <CardDescription>Define who you want to reach with this campaign</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="ageRange">Age Range</Label>
                <Select
                  value={formData.targetAudience.demographics.ageRange}
                  onValueChange={(value) =>
                    setFormData({
                      ...formData,
                      targetAudience: {
                        ...formData.targetAudience,
                        demographics: { ...formData.targetAudience.demographics, ageRange: value },
                      },
                    })
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select age range" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="18-24">18-24</SelectItem>
                    <SelectItem value="25-34">25-34</SelectItem>
                    <SelectItem value="35-44">35-44</SelectItem>
                    <SelectItem value="45-54">45-54</SelectItem>
                    <SelectItem value="55-64">55-64</SelectItem>
                    <SelectItem value="65+">65+</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="gender">Gender</Label>
                <Select
                  value={formData.targetAudience.demographics.gender}
                  onValueChange={(value) =>
                    setFormData({
                      ...formData,
                      targetAudience: {
                        ...formData.targetAudience,
                        demographics: { ...formData.targetAudience.demographics, gender: value },
                      },
                    })
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select gender" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All</SelectItem>
                    <SelectItem value="male">Male</SelectItem>
                    <SelectItem value="female">Female</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label>Interests</Label>
              <div className="flex space-x-2">
                <Input
                  value={currentInterest}
                  onChange={(e) => setCurrentInterest(e.target.value)}
                  placeholder="Add interest (e.g., technology, fitness)"
                  onKeyPress={(e) => e.key === "Enter" && (e.preventDefault(), addInterest())}
                />
                <Button type="button" onClick={addInterest} variant="outline">
                  Add
                </Button>
              </div>
              <div className="flex flex-wrap gap-2 mt-2">
                {formData.targetAudience.demographics.interests.map((interest) => (
                  <Badge
                    key={interest}
                    variant="secondary"
                    className="cursor-pointer"
                    onClick={() => removeInterest(interest)}
                  >
                    {interest} ×
                  </Badge>
                ))}
              </div>
            </div>

            <div className="space-y-2">
              <Label>Locations</Label>
              <div className="flex space-x-2">
                <Input
                  value={currentLocation}
                  onChange={(e) => setCurrentLocation(e.target.value)}
                  placeholder="Add location (e.g., United States, London)"
                  onKeyPress={(e) => e.key === "Enter" && (e.preventDefault(), addLocation())}
                />
                <Button type="button" onClick={addLocation} variant="outline">
                  Add
                </Button>
              </div>
              <div className="flex flex-wrap gap-2 mt-2">
                {formData.targetAudience.demographics.locations.map((location) => (
                  <Badge
                    key={location}
                    variant="secondary"
                    className="cursor-pointer"
                    onClick={() => removeLocation(location)}
                  >
                    {location} ×
                  </Badge>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Submit Buttons */}
        <div className="flex items-center justify-end space-x-4">
          <Button type="button" variant="outline" onClick={() => router.back()}>
            Cancel
          </Button>
          <Button type="submit" variant="outline" disabled={loading}>
            <Save className="mr-2 h-4 w-4" />
            Save Draft
          </Button>
          <Button type="submit" disabled={loading}>
            {loading ? (
              <>
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-background border-t-transparent" />
                Creating...
              </>
            ) : (
              <>
                <Play className="mr-2 h-4 w-4" />
                Create & Launch
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  )
}

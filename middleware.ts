import { with<PERSON><PERSON> } from "next-auth/middleware"
import { NextResponse } from "next/server"

export default withAuth(
  function middleware(req) {
    const { pathname } = req.nextUrl
    const token = req.nextauth.token

    // Redirect to dashboard if accessing root and authenticated
    if (pathname === "/" && token) {
      return NextResponse.redirect(new URL("/dashboard", req.url))
    }

    // Redirect to signin if accessing protected routes without auth
    if (!token && pathname.startsWith("/dashboard")) {
      return NextResponse.redirect(new URL("/auth/signin", req.url))
    }

    return NextResponse.next()
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        const { pathname } = req.nextUrl

        // Allow access to auth pages and API routes
        if (pathname.startsWith("/auth") || pathname.startsWith("/api/auth") || pathname === "/") {
          return true
        }

        // Require authentication for all other routes
        return !!token
      },
    },
  },
)

export const config = {
  matcher: ["/((?!api/auth|_next/static|_next/image|favicon.ico|public).*)"],
}

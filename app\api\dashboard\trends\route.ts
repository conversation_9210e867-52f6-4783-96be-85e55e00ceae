import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.tenantId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const period = searchParams.get("period") || "30d"

    const now = new Date()
    const daysBack = period === "7d" ? 7 : period === "90d" ? 90 : 30
    const currentPeriodStart = new Date(now.getTime() - daysBack * 24 * 60 * 60 * 1000)
    const previousPeriodStart = new Date(currentPeriodStart.getTime() - daysBack * 24 * 60 * 60 * 1000)

    const tenantId = session.user.tenantId

    // Get current period data
    const currentData = await prisma.analyticsEvent.groupBy({
      by: ["eventType"],
      where: {
        tenantId,
        createdAt: { gte: currentPeriodStart },
      },
      _sum: { value: true },
    })

    // Get previous period data
    const previousData = await prisma.analyticsEvent.groupBy({
      by: ["eventType"],
      where: {
        tenantId,
        createdAt: {
          gte: previousPeriodStart,
          lt: currentPeriodStart,
        },
      },
      _sum: { value: true },
    })

    // Process data
    const currentMap = currentData.reduce(
      (acc, item) => {
        acc[item.eventType] = item._sum.value || 0
        return acc
      },
      {} as Record<string, number>,
    )

    const previousMap = previousData.reduce(
      (acc, item) => {
        acc[item.eventType] = item._sum.value || 0
        return acc
      },
      {} as Record<string, number>,
    )

    // Calculate trends
    const calculateTrend = (current: number, previous: number) => {
      if (previous === 0) return current > 0 ? 100 : 0
      return ((current - previous) / previous) * 100
    }

    const currentImpressions = currentMap.impression || 0
    const previousImpressions = previousMap.impression || 0
    const currentEngagement =
      (currentMap.like || 0) + (currentMap.comment || 0) + (currentMap.share || 0) + (currentMap.reaction || 0)
    const previousEngagement =
      (previousMap.like || 0) + (previousMap.comment || 0) + (previousMap.share || 0) + (previousMap.reaction || 0)
    const currentReach = currentMap.reach || 0
    const previousReach = previousMap.reach || 0
    const currentClicks = currentMap.click || 0
    const previousClicks = previousMap.click || 0
    const currentConversions = currentMap.conversion || 0
    const previousConversions = previousMap.conversion || 0

    const trends = {
      impressions: calculateTrend(currentImpressions, previousImpressions),
      engagement: calculateTrend(currentEngagement, previousEngagement),
      reach: calculateTrend(currentReach, previousReach),
      clicks: calculateTrend(currentClicks, previousClicks),
      conversions: calculateTrend(currentConversions, previousConversions),
    }

    return NextResponse.json({ trends })
  } catch (error) {
    console.error("Failed to fetch trends:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { aiProviderManager } from "@/lib/ai-providers"
import { z } from "zod"

const generateContentSchema = z.object({
  providerId: z.string().uuid(),
  model: z.string(),
  prompt: z.string().min(1),
  contentType: z.enum(["post", "caption", "hashtags", "title"]),
  platform: z.string().optional(),
  tone: z.enum(["professional", "casual", "friendly", "formal", "creative"]).optional(),
  language: z.string().default("en"),
  maxLength: z.number().positive().optional(),
})

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.tenantId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = generateContentSchema.parse(body)

    // Initialize AI providers for the tenant
    await aiProviderManager.initializeProviders(session.user.tenantId)

    // Build system prompt based on content type and parameters
    const systemPrompt = buildSystemPrompt(validatedData)

    // Generate content using AI
    const result = await aiProviderManager.generateContent(
      validatedData.providerId,
      validatedData.model,
      validatedData.prompt,
      {
        system: systemPrompt,
        temperature: 0.7,
        maxTokens: validatedData.maxLength || 500,
      },
      session.user.id, // Pass real user ID
    )

    // Post-process the generated content
    const processedContent = postProcessContent(result.text, validatedData)

    return NextResponse.json({
      content: processedContent,
      usage: result.usage,
      model: validatedData.model,
      provider: validatedData.providerId,
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: "Validation error", details: error.errors }, { status: 400 })
    }

    console.error("AI generation failed:", error)
    return NextResponse.json(
      { error: "AI generation failed", details: error instanceof Error ? error.message : "Unknown error" },
      { status: 500 },
    )
  }
}

function buildSystemPrompt(data: z.infer<typeof generateContentSchema>): string {
  let prompt = `You are an expert social media content creator. `

  switch (data.contentType) {
    case "post":
      prompt += `Create engaging social media posts that drive engagement and conversions. `
      break
    case "caption":
      prompt += `Write compelling captions that complement visual content. `
      break
    case "hashtags":
      prompt += `Generate relevant and trending hashtags. Return only hashtags separated by spaces. `
      break
    case "title":
      prompt += `Create attention-grabbing titles and headlines. `
      break
  }

  if (data.platform) {
    prompt += `Optimize for ${data.platform} platform best practices. `
  }

  if (data.tone) {
    prompt += `Use a ${data.tone} tone. `
  }

  if (data.language !== "en") {
    prompt += `Write in ${data.language} language. `
  }

  if (data.maxLength) {
    prompt += `Keep the content under ${data.maxLength} characters. `
  }

  prompt += `Focus on creating high-quality, engaging content that resonates with the target audience.`

  return prompt
}

function postProcessContent(content: string, data: z.infer<typeof generateContentSchema>): string {
  let processed = content.trim()

  // Remove quotes if present
  if (processed.startsWith('"') && processed.endsWith('"')) {
    processed = processed.slice(1, -1)
  }

  // Ensure hashtags format
  if (data.contentType === "hashtags") {
    processed = processed
      .split(/\s+/)
      .map((tag) => (tag.startsWith("#") ? tag : `#${tag}`))
      .join(" ")
  }

  // Truncate if needed
  if (data.maxLength && processed.length > data.maxLength) {
    processed = processed.substring(0, data.maxLength - 3) + "..."
  }

  return processed
}

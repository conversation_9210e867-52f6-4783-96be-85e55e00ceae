import { generateText } from "ai"
import { openai } from "@ai-sdk/openai"
import { anthropic } from "@ai-sdk/anthropic"
import { google } from "@ai-sdk/google"
import { groq } from "@ai-sdk/groq"
import { xai } from "@ai-sdk/xai"
import { deepseek } from "@ai-sdk/deepseek"
import { prisma } from "./prisma"
import { decrypt } from "./encryption"

export interface AIProviderConfig {
  id: string
  name: string
  type: string
  apiKey: string
  baseUrl?: string
  models: string[]
  description: string
  icon: string
  pricing: {
    inputTokens: number
    outputTokens: number
  }
  capabilities: string[]
  recommended: boolean
}

export const SUPPORTED_PROVIDERS = {
  openai: {
    name: "OpenAI",
    description: "Industry-leading AI with GPT models",
    icon: "🤖",
    baseUrl: "https://api.openai.com/v1",
    models: ["gpt-4o", "gpt-4-turbo", "gpt-3.5-turbo"],
    capabilities: ["text", "vision", "function-calling"],
    pricing: { inputTokens: 0.00001, outputTokens: 0.00003 },
    recommended: true,
    setup: {
      title: "Connect OpenAI",
      description: "Get your API key from OpenAI Dashboard",
      steps: [
        "Go to platform.openai.com",
        "Click 'API Keys' in sidebar",
        "Create new secret key",
        "Copy and paste below",
      ],
      helpUrl: "https://platform.openai.com/api-keys",
    },
  },
  anthropic: {
    name: "Anthropic Claude",
    description: "Advanced reasoning and safety-focused AI",
    icon: "🧠",
    baseUrl: "https://api.anthropic.com",
    models: ["claude-3-5-sonnet-20241022", "claude-3-haiku-20240307", "claude-3-opus-20240229"],
    capabilities: ["text", "vision", "long-context"],
    pricing: { inputTokens: 0.000003, outputTokens: 0.000015 },
    recommended: true,
    setup: {
      title: "Connect Claude",
      description: "Get your API key from Anthropic Console",
      steps: ["Go to console.anthropic.com", "Navigate to API Keys", "Generate new key", "Copy and paste below"],
      helpUrl: "https://console.anthropic.com/",
    },
  },
  google: {
    name: "Google Gemini",
    description: "Google's multimodal AI with strong reasoning",
    icon: "✨",
    baseUrl: "https://generativelanguage.googleapis.com",
    models: ["gemini-1.5-pro", "gemini-1.5-flash", "gemini-pro"],
    capabilities: ["text", "vision", "multimodal"],
    pricing: { inputTokens: 0.000001, outputTokens: 0.000002 },
    recommended: true,
    setup: {
      title: "Connect Gemini",
      description: "Get your API key from Google AI Studio",
      steps: ["Go to aistudio.google.com", "Click 'Get API key'", "Create new key", "Copy and paste below"],
      helpUrl: "https://aistudio.google.com/app/apikey",
    },
  },
  groq: {
    name: "Groq",
    description: "Ultra-fast AI inference with lightning speed",
    icon: "⚡",
    baseUrl: "https://api.groq.com/openai/v1",
    models: ["llama-3.1-70b-versatile", "llama-3.1-8b-instant", "mixtral-8x7b-32768"],
    capabilities: ["text", "ultra-fast", "open-source"],
    pricing: { inputTokens: 0.0000005, outputTokens: 0.000001 },
    recommended: true,
    setup: {
      title: "Connect Groq",
      description: "Get your API key from Groq Console",
      steps: ["Go to console.groq.com", "Navigate to API Keys", "Create new key", "Copy and paste below"],
      helpUrl: "https://console.groq.com/keys",
    },
  },
  xai: {
    name: "xAI Grok",
    description: "Elon Musk's AI with real-time knowledge",
    icon: "🚀",
    baseUrl: "https://api.x.ai/v1",
    models: ["grok-beta", "grok-vision-beta"],
    capabilities: ["text", "real-time", "vision"],
    pricing: { inputTokens: 0.000005, outputTokens: 0.000015 },
    recommended: true,
    setup: {
      title: "Connect Grok",
      description: "Get your API key from xAI Console",
      steps: ["Go to console.x.ai", "Navigate to API Keys", "Generate new key", "Copy and paste below"],
      helpUrl: "https://console.x.ai/",
    },
  },
  deepseek: {
    name: "DeepSeek",
    description: "Cost-effective AI with strong coding abilities",
    icon: "🔍",
    baseUrl: "https://api.deepseek.com/v1",
    models: ["deepseek-chat", "deepseek-coder"],
    capabilities: ["text", "coding", "cost-effective"],
    pricing: { inputTokens: 0.0000001, outputTokens: 0.0000002 },
    recommended: false,
    setup: {
      title: "Connect DeepSeek",
      description: "Get your API key from DeepSeek Platform",
      steps: ["Go to platform.deepseek.com", "Navigate to API Keys", "Create new key", "Copy and paste below"],
      helpUrl: "https://platform.deepseek.com/",
    },
  },
  openrouter: {
    name: "OpenRouter",
    description: "Access 100+ AI models through one API",
    icon: "🌐",
    baseUrl: "https://openrouter.ai/api/v1",
    models: ["anthropic/claude-3.5-sonnet", "meta-llama/llama-3.1-405b", "google/gemini-pro-1.5"],
    capabilities: ["text", "multi-model", "routing"],
    pricing: { inputTokens: 0.000002, outputTokens: 0.000006 },
    recommended: false,
    setup: {
      title: "Connect OpenRouter",
      description: "Get your API key from OpenRouter",
      steps: ["Go to openrouter.ai", "Sign up and navigate to Keys", "Create new API key", "Copy and paste below"],
      helpUrl: "https://openrouter.ai/keys",
    },
  },
}

export class EnhancedAIProviderManager {
  private providers: Map<string, any> = new Map()

  async initializeProviders(tenantId: string) {
    const dbProviders = await prisma.aiProvider.findMany({
      where: { tenantId, isActive: true },
    })

    for (const provider of dbProviders) {
      const apiKey = decrypt(provider.apiKeyEncrypted)

      switch (provider.providerType) {
        case "openai":
          this.providers.set(provider.id, openai)
          break
        case "anthropic":
          this.providers.set(provider.id, anthropic)
          break
        case "google":
          this.providers.set(provider.id, google)
          break
        case "groq":
          this.providers.set(provider.id, groq)
          break
        case "xai":
          this.providers.set(provider.id, xai)
          break
        case "deepseek":
          this.providers.set(provider.id, deepseek)
          break
        case "openrouter":
          // OpenRouter uses OpenAI-compatible API
          this.providers.set(provider.id, openai)
          break
      }
    }
  }

  async getRecommendedProvider(contentType: string, tenantId: string) {
    const providers = await prisma.aiProvider.findMany({
      where: { tenantId, isActive: true, healthStatus: "healthy" },
      orderBy: { createdAt: "asc" },
    })

    // Smart provider recommendation based on content type
    const recommendations = {
      social_post: ["openai", "anthropic", "groq"],
      long_content: ["anthropic", "google"],
      creative: ["openai", "xai"],
      technical: ["deepseek", "groq"],
      multilingual: ["google", "openai"],
    }

    const preferredTypes = recommendations[contentType as keyof typeof recommendations] || ["openai"]

    for (const type of preferredTypes) {
      const provider = providers.find((p) => p.providerType === type)
      if (provider) return provider
    }

    return providers[0] // Fallback to first available
  }

  async generateContentWithFallback(
    prompt: string,
    options: {
      contentType?: string
      tone?: string
      platform?: string
      maxLength?: number
    },
    tenantId: string,
    userId: string,
  ) {
    const recommendedProvider = await this.getRecommendedProvider(options.contentType || "social_post", tenantId)

    if (!recommendedProvider) {
      throw new Error("No AI providers available. Please connect an AI provider first.")
    }

    try {
      return await this.generateContent(
        recommendedProvider.id,
        recommendedProvider.models[0],
        prompt,
        {
          system: this.buildSystemPrompt(options),
          temperature: 0.7,
          maxTokens: options.maxLength || 500,
        },
        userId,
      )
    } catch (error) {
      // Try fallback providers
      const fallbackProviders = await prisma.aiProvider.findMany({
        where: {
          tenantId,
          isActive: true,
          healthStatus: "healthy",
          id: { not: recommendedProvider.id },
        },
        take: 2,
      })

      for (const fallback of fallbackProviders) {
        try {
          return await this.generateContent(
            fallback.id,
            fallback.models[0],
            prompt,
            {
              system: this.buildSystemPrompt(options),
              temperature: 0.7,
              maxTokens: options.maxLength || 500,
            },
            userId,
          )
        } catch (fallbackError) {
          continue
        }
      }

      throw error
    }
  }

  private buildSystemPrompt(options: any): string {
    let prompt = "You are an expert social media content creator. "

    if (options.contentType) {
      const contentPrompts = {
        social_post: "Create engaging social media posts that drive engagement.",
        caption: "Write compelling captions that complement visual content.",
        hashtags: "Generate relevant trending hashtags. Return only hashtags separated by spaces.",
        title: "Create attention-grabbing titles and headlines.",
        long_content: "Write detailed, informative content.",
        creative: "Be creative and think outside the box.",
      }
      prompt += contentPrompts[options.contentType as keyof typeof contentPrompts] || ""
    }

    if (options.platform) {
      const platformPrompts = {
        facebook: "Optimize for Facebook's algorithm with engaging, shareable content.",
        twitter: "Keep it concise and punchy for Twitter's fast-paced environment.",
        linkedin: "Use a professional tone suitable for LinkedIn's business audience.",
        instagram: "Focus on visual storytelling and use relevant hashtags.",
      }
      prompt += ` ${platformPrompts[options.platform as keyof typeof platformPrompts] || ""}`
    }

    if (options.tone) {
      prompt += ` Use a ${options.tone} tone.`
    }

    return prompt
  }

  async generateContent(
    providerId: string,
    model: string,
    prompt: string,
    options: {
      system?: string
      temperature?: number
      maxTokens?: number
    } = {},
    userId: string,
  ) {
    const provider = this.providers.get(providerId)
    if (!provider) {
      throw new Error(`Provider ${providerId} not found`)
    }

    try {
      const result = await generateText({
        model: provider(model),
        prompt,
        system: options.system,
        temperature: options.temperature,
        maxTokens: options.maxTokens,
      })

      await this.trackUsage(providerId, model, result.usage, userId)
      return result
    } catch (error) {
      console.error(`AI generation failed for provider ${providerId}:`, error)

      await prisma.aiProvider.update({
        where: { id: providerId },
        data: {
          healthStatus: "degraded",
          lastHealthCheck: new Date(),
        },
      })

      throw error
    }
  }

  private async trackUsage(providerId: string, model: string, usage: any, userId: string) {
    const provider = await prisma.aiProvider.findUnique({
      where: { id: providerId },
    })

    if (provider) {
      const cost = await this.calculateCost(provider.providerType, model, usage.totalTokens || 0)

      await prisma.aiUsage.create({
        data: {
          tenantId: provider.tenantId,
          userId: userId,
          provider: provider.providerType,
          model,
          tokensUsed: usage.totalTokens || 0,
          cost,
          requestType: "text_generation",
        },
      })

      await prisma.aiProvider.update({
        where: { id: providerId },
        data: {
          healthStatus: "healthy",
          lastHealthCheck: new Date(),
        },
      })
    }
  }

  private async calculateCost(provider: string, model: string, tokens: number): Promise<number> {
    const providerConfig = SUPPORTED_PROVIDERS[provider as keyof typeof SUPPORTED_PROVIDERS]
    if (providerConfig) {
      return providerConfig.pricing.inputTokens * tokens
    }
    return 0.000001 * tokens
  }
}

export const enhancedAIProviderManager = new EnhancedAIProviderManager()

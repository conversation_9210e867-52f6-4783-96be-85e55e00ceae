import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { subDays, format } from "date-fns"

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const days = Number.parseInt(searchParams.get("days") || "30")

    const startDate = subDays(new Date(), days)
    const tenantId = session.user.tenantId

    const engagementData = await prisma.analyticsEvent.findMany({
      where: {
        tenantId,
        eventType: { in: ["like", "comment", "share", "reaction"] },
        createdAt: { gte: startDate },
      },
      select: {
        createdAt: true,
        value: true,
        eventType: true,
      },
      orderBy: { createdAt: "asc" },
    })

    // Group by date
    const chartData = engagementData.reduce(
      (acc, event) => {
        const date = format(event.createdAt, "yyyy-MM-dd")
        if (!acc[date]) {
          acc[date] = { date, engagement: 0, likes: 0, comments: 0, shares: 0 }
        }
        acc[date].engagement += event.value
        if (event.eventType === "like") acc[date].likes += event.value
        if (event.eventType === "comment") acc[date].comments += event.value
        if (event.eventType === "share") acc[date].shares += event.value
        return acc
      },
      {} as Record<string, any>,
    )

    const data = Object.values(chartData).sort(
      (a: any, b: any) => new Date(a.date).getTime() - new Date(b.date).getTime(),
    )

    return NextResponse.json({ data })
  } catch (error) {
    console.error("Failed to fetch engagement chart data:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

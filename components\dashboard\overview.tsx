"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { TrendingUp, TrendingDown, Target, BarChart3, FileText, MousePointer } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { formatNumber, formatCurrency } from "@/lib/utils"

interface OverviewMetrics {
  totalCampaigns: number
  activeCampaigns: number
  publishedPosts: number
  totalPosts: number
  totalEngagement: number
  engagementRate: number
  clickThroughRate: number
  costPerClick: number
  totalSpent: number
  totalBudget: number
  impressions: number
  reach: number
  clicks: number
  conversions: number
}

interface TrendData {
  impressions: number
  engagement: number
  reach: number
  clicks: number
  conversions: number
}

interface TopPerformer {
  id: string
  title: string
  platform: string
  engagement: number
  impressions: number
  type: string
  engagementRate: number
}

interface CampaignBreakdown {
  id: string
  name: string
  spent: number
  budget: number
  performance: number
  status: string
  roi: number
}

export function Overview() {
  const [metrics, setMetrics] = useState<OverviewMetrics | null>(null)
  const [trends, setTrends] = useState<TrendData | null>(null)
  const [topPerformers, setTopPerformers] = useState<TopPerformer[]>([])
  const [campaignBreakdown, setCampaignBreakdown] = useState<CampaignBreakdown[]>([])
  const [loading, setLoading] = useState(true)
  const [period, setPeriod] = useState("30d")
  const { toast } = useToast()

  useEffect(() => {
    fetchOverviewData()
  }, [period])

  async function fetchOverviewData() {
    try {
      setLoading(true)

      // Fetch all data in parallel
      const [metricsRes, trendsRes, topPostsRes, campaignPerfRes] = await Promise.all([
        fetch(`/api/dashboard/metrics?period=${period}`),
        fetch(`/api/dashboard/trends?period=${period}`),
        fetch(`/api/dashboard/top-posts?period=${period}`),
        fetch(`/api/dashboard/campaign-performance?period=${period}`),
      ])

      if (!metricsRes.ok || !trendsRes.ok || !topPostsRes.ok || !campaignPerfRes.ok) {
        throw new Error("Failed to fetch dashboard data")
      }

      const [metricsData, trendsData, topPostsData, campaignPerfData] = await Promise.all([
        metricsRes.json(),
        trendsRes.json(),
        topPostsRes.json(),
        campaignPerfRes.json(),
      ])

      setMetrics(metricsData.metrics)
      setTrends(trendsData.trends)
      setTopPerformers(topPostsData.posts)
      setCampaignBreakdown(campaignPerfData.campaigns)
    } catch (error) {
      console.error("Failed to fetch overview data:", error)
      toast({
        title: "Error",
        description: "Failed to load dashboard data. Please try again.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <div className="h-8 w-48 bg-muted animate-pulse rounded mb-2" />
            <div className="h-4 w-64 bg-muted animate-pulse rounded" />
          </div>
          <div className="h-10 w-32 bg-muted animate-pulse rounded" />
        </div>

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="h-4 w-24 bg-muted animate-pulse rounded" />
                <div className="h-4 w-4 bg-muted animate-pulse rounded" />
              </CardHeader>
              <CardContent>
                <div className="h-8 w-16 bg-muted animate-pulse rounded mb-2" />
                <div className="h-3 w-32 bg-muted animate-pulse rounded" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (!metrics || !trends) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-medium text-muted-foreground mb-2">No data available</h3>
        <p className="text-muted-foreground">Start creating campaigns to see your analytics</p>
      </div>
    )
  }

  const cards = [
    {
      title: "Total Campaigns",
      value: metrics.totalCampaigns,
      change: `${metrics.activeCampaigns} active`,
      icon: Target,
      trend: metrics.activeCampaigns > 0 ? "up" : "neutral",
      percentage: trends.conversions,
    },
    {
      title: "Published Posts",
      value: metrics.publishedPosts,
      change: `${metrics.totalPosts - metrics.publishedPosts} drafts`,
      icon: FileText,
      trend: metrics.publishedPosts > metrics.totalPosts * 0.7 ? "up" : "down",
      percentage: trends.engagement,
    },
    {
      title: "Total Engagement",
      value: formatNumber(metrics.totalEngagement),
      change: `${metrics.engagementRate.toFixed(2)}% rate`,
      icon: BarChart3,
      trend: metrics.engagementRate > 3 ? "up" : "down",
      percentage: trends.engagement,
    },
    {
      title: "Click-Through Rate",
      value: `${metrics.clickThroughRate.toFixed(2)}%`,
      change: `${formatCurrency(metrics.costPerClick)} CPC`,
      icon: MousePointer,
      trend: metrics.clickThroughRate > 2 ? "up" : "down",
      percentage: trends.clicks,
    },
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-2xl font-bold">Analytics Overview</h3>
          <p className="text-muted-foreground">Comprehensive performance metrics across all campaigns</p>
        </div>
        <Select value={period} onValueChange={setPeriod}>
          <SelectTrigger className="w-32">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="7d">Last 7 days</SelectItem>
            <SelectItem value="30d">Last 30 days</SelectItem>
            <SelectItem value="90d">Last 90 days</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {cards.map((card, index) => (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{card.title}</CardTitle>
              <card.icon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{card.value}</div>
              <div className="flex items-center text-xs text-muted-foreground">
                {card.trend === "up" && <TrendingUp className="h-3 w-3 mr-1 text-green-500" />}
                {card.trend === "down" && <TrendingDown className="h-3 w-3 mr-1 text-red-500" />}
                <span className="mr-2">{card.change}</span>
                {card.percentage !== undefined && (
                  <span className={card.percentage >= 0 ? "text-green-600" : "text-red-600"}>
                    {card.percentage > 0 ? "+" : ""}
                    {card.percentage.toFixed(1)}%
                  </span>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Detailed Metrics */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Performance Metrics</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Total Impressions</span>
              <span className="text-lg font-semibold">{formatNumber(metrics.impressions)}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Total Reach</span>
              <span className="text-lg font-semibold">{formatNumber(metrics.reach)}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Total Clicks</span>
              <span className="text-lg font-semibold">{formatNumber(metrics.clicks)}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Conversions</span>
              <span className="text-lg font-semibold">{metrics.conversions}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Budget Overview</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Total Budget</span>
              <span className="text-lg font-semibold">{formatCurrency(metrics.totalBudget)}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Total Spent</span>
              <span className="text-lg font-semibold">{formatCurrency(metrics.totalSpent)}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Remaining</span>
              <span className="text-lg font-semibold">{formatCurrency(metrics.totalBudget - metrics.totalSpent)}</span>
            </div>
            <Progress value={(metrics.totalSpent / metrics.totalBudget) * 100} className="h-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Performance Trends</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Impressions</span>
              <div className="flex items-center space-x-2">
                {trends.impressions >= 0 ? (
                  <TrendingUp className="h-4 w-4 text-green-600" />
                ) : (
                  <TrendingDown className="h-4 w-4 text-red-600" />
                )}
                <span className={trends.impressions >= 0 ? "text-green-600" : "text-red-600"}>
                  {trends.impressions > 0 ? "+" : ""}
                  {trends.impressions.toFixed(1)}%
                </span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Engagement</span>
              <div className="flex items-center space-x-2">
                {trends.engagement >= 0 ? (
                  <TrendingUp className="h-4 w-4 text-green-600" />
                ) : (
                  <TrendingDown className="h-4 w-4 text-red-600" />
                )}
                <span className={trends.engagement >= 0 ? "text-green-600" : "text-red-600"}>
                  {trends.engagement > 0 ? "+" : ""}
                  {trends.engagement.toFixed(1)}%
                </span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Clicks</span>
              <div className="flex items-center space-x-2">
                {trends.clicks >= 0 ? (
                  <TrendingUp className="h-4 w-4 text-green-600" />
                ) : (
                  <TrendingDown className="h-4 w-4 text-red-600" />
                )}
                <span className={trends.clicks >= 0 ? "text-green-600" : "text-red-600"}>
                  {trends.clicks > 0 ? "+" : ""}
                  {trends.clicks.toFixed(1)}%
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Top Performers and Campaign Breakdown */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Top Performing Content</CardTitle>
            <CardDescription>Your best performing posts across all platforms</CardDescription>
          </CardHeader>
          <CardContent>
            {topPerformers.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground">No posts published yet</p>
              </div>
            ) : (
              <div className="space-y-4">
                {topPerformers.slice(0, 5).map((post, index) => (
                  <div key={post.id} className="flex items-center space-x-4">
                    <div className="flex-shrink-0 w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                      <span className="text-sm font-semibold text-primary">#{index + 1}</span>
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">{post.title}</p>
                      <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                        <Badge variant="outline" className="capitalize">
                          {post.platform}
                        </Badge>
                        <span>{formatNumber(post.impressions)} impressions</span>
                        <span>{post.engagementRate.toFixed(2)}% rate</span>
                      </div>
                    </div>
                    <div className="flex-shrink-0 text-right">
                      <div className="text-sm font-semibold">{formatNumber(post.engagement)}</div>
                      <div className="text-xs text-muted-foreground">engagements</div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Campaign Performance</CardTitle>
            <CardDescription>Budget utilization and ROI by campaign</CardDescription>
          </CardHeader>
          <CardContent>
            {campaignBreakdown.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground">No campaigns created yet</p>
              </div>
            ) : (
              <div className="space-y-4">
                {campaignBreakdown.slice(0, 5).map((campaign) => (
                  <div key={campaign.id} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <span className="text-sm font-medium truncate">{campaign.name}</span>
                        <Badge variant={campaign.status === "active" ? "default" : "secondary"} className="text-xs">
                          {campaign.status}
                        </Badge>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-semibold">
                          {formatCurrency(campaign.spent)} / {formatCurrency(campaign.budget)}
                        </div>
                        <div className="text-xs text-muted-foreground">ROI: {campaign.roi.toFixed(1)}x</div>
                      </div>
                    </div>
                    <Progress value={(campaign.spent / campaign.budget) * 100} className="h-2" />
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

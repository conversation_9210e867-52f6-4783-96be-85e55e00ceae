import { type NextRequest, NextResponse } from "next/server"
import bcrypt from "bcryptjs"
import { nanoid } from "nanoid"
import { prisma } from "@/lib/prisma"
import { z } from "zod"

const signupSchema = z.object({
  name: z.string().min(2).max(100),
  email: z.string().email(),
  password: z.string().min(8),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { name, email, password } = signupSchema.parse(body)

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email },
    })

    if (existingUser) {
      return NextResponse.json({ error: "User with this email already exists" }, { status: 400 })
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12)

    // Create tenant for the user
    const tenant = await prisma.tenant.create({
      data: {
        name: `${name}'s Workspace`,
        domain: nanoid(8).toLowerCase(),
        settings: {
          timezone: "UTC",
          currency: "USD",
          features: {
            aiGeneration: true,
            socialPublishing: true,
            analytics: true,
            teamCollaboration: true,
          },
        },
      },
    })

    // Create user
    const user = await prisma.user.create({
      data: {
        name,
        email,
        password: hashedPassword,
        tenantId: tenant.id,
        role: "admin",
        emailVerified: new Date(),
      },
    })

    // Create team member record
    await prisma.teamMember.create({
      data: {
        tenantId: tenant.id,
        userId: user.id,
        role: "admin",
        permissions: ["all"],
      },
    })

    return NextResponse.json({ message: "User created successfully", userId: user.id }, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: "Invalid input", details: error.errors }, { status: 400 })
    }

    console.error("Signup error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

"use client"

import { CardDescription } from "@/components/ui/card"

import { useEffect, useState } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { TrendingUp, TrendingDown, Target, BarChart3, FileText, MousePointer } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { formatNumber, formatCurrency } from "@/lib/utils"
import type { DashboardMetrics } from "@/lib/types"

interface OverviewData {
  metrics: {
    impressions: number
    engagement: number
    reach: number
    clicks: number
    conversions: number
    spent: number
    ctr: number
    cpc: number
    cpm: number
    roas: number
  }
  trends: {
    impressions: number
    engagement: number
    reach: number
    clicks: number
  }
  topPerformers: Array<{
    id: string
    title: string
    platform: string
    engagement: number
    impressions: number
    type: string
  }>
  campaignBreakdown: Array<{
    id: string
    name: string
    spent: number
    budget: number
    performance: number
    status: string
  }>
}

export function Overview() {
  const [data, setData] = useState<OverviewData | null>(null)
  const [loading, setLoading] = useState(true)
  const [period, setPeriod] = useState("30d")
  const [metrics, setMetrics] = useState<DashboardMetrics | null>(null)
  const { toast } = useToast()

  useEffect(() => {
    fetchOverviewData()
    async function fetchMetrics() {
      try {
        const response = await fetch("/api/dashboard/metrics")
        if (response.ok) {
          const data = await response.json()
          setMetrics(data.metrics)
        }
      } catch (error) {
        console.error("Failed to fetch metrics:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchMetrics()
  }, [period])

  async function fetchOverviewData() {
    try {
      setLoading(true)
      const response = await fetch(`/api/analytics/overview?period=${period}`)

      if (!response.ok) {
        throw new Error("Failed to fetch overview data")
      }

      const result = await response.json()
      setData(result)
    } catch (error) {
      console.error("Failed to fetch overview data:", error)
      toast({
        title: "Error",
        description: "Failed to load analytics data",
        variant: "destructive",
      })

      // Set mock data for development
      setData({
        metrics: {
          impressions: 125430,
          engagement: 8920,
          reach: 89340,
          clicks: 2340,
          conversions: 156,
          spent: 2450.5,
          ctr: 1.87,
          cpc: 1.05,
          cpm: 19.54,
          roas: 4.2,
        },
        trends: {
          impressions: 12.5,
          engagement: -3.2,
          reach: 8.7,
          clicks: 15.3,
        },
        topPerformers: [
          {
            id: "1",
            title: "Summer Product Launch Announcement",
            platform: "facebook",
            engagement: 1250,
            impressions: 15600,
            type: "post",
          },
          {
            id: "2",
            title: "Behind the Scenes: Product Development",
            platform: "instagram",
            engagement: 980,
            impressions: 12300,
            type: "story",
          },
          {
            id: "3",
            title: "Customer Success Story",
            platform: "linkedin",
            engagement: 750,
            impressions: 8900,
            type: "post",
          },
        ],
        campaignBreakdown: [
          {
            id: "1",
            name: "Summer Product Launch",
            spent: 1250.0,
            budget: 2000.0,
            performance: 87,
            status: "active",
          },
          {
            id: "2",
            name: "Brand Awareness Q4",
            spent: 800.5,
            budget: 1500.0,
            performance: 72,
            status: "active",
          },
          {
            id: "3",
            name: "Holiday Campaign",
            spent: 400.0,
            budget: 1000.0,
            performance: 45,
            status: "paused",
          },
        ],
      })
    } finally {
      setLoading(false)
    }
  }

  if (loading || !metrics) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="h-4 w-24 bg-muted animate-pulse rounded" />
              <div className="h-4 w-4 bg-muted animate-pulse rounded" />
            </CardHeader>
            <CardContent>
              <div className="h-8 w-16 bg-muted animate-pulse rounded mb-2" />
              <div className="h-3 w-32 bg-muted animate-pulse rounded" />
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  const cards = [
    {
      title: "Total Campaigns",
      value: metrics.totalCampaigns,
      change: `${metrics.activeCampaigns} active`,
      icon: Target,
      trend: metrics.activeCampaigns > 0 ? "up" : "neutral",
    },
    {
      title: "Published Posts",
      value: metrics.publishedPosts,
      change: `${metrics.totalPosts - metrics.publishedPosts} drafts`,
      icon: FileText,
      trend: metrics.publishedPosts > metrics.totalPosts * 0.7 ? "up" : "down",
    },
    {
      title: "Total Engagement",
      value: formatNumber(metrics.totalEngagement),
      change: `${metrics.engagementRate}% rate`,
      icon: BarChart3,
      trend: metrics.engagementRate > 3 ? "up" : "down",
    },
    {
      title: "Click-Through Rate",
      value: `${metrics.clickThroughRate}%`,
      change: `${formatCurrency(metrics.costPerClick)} CPC`,
      icon: MousePointer,
      trend: metrics.clickThroughRate > 2 ? "up" : "down",
    },
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-2xl font-bold">Analytics Overview</h3>
          <p className="text-muted-foreground">Comprehensive performance metrics across all campaigns</p>
        </div>
        <Select value={period} onValueChange={setPeriod}>
          <SelectTrigger className="w-32">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="7d">Last 7 days</SelectItem>
            <SelectItem value="30d">Last 30 days</SelectItem>
            <SelectItem value="90d">Last 90 days</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {cards.map((card, index) => (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{card.title}</CardTitle>
              <card.icon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{card.value}</div>
              <div className="flex items-center text-xs text-muted-foreground">
                {card.trend === "up" && <TrendingUp className="h-3 w-3 mr-1 text-green-500" />}
                {card.trend === "down" && <TrendingDown className="h-3 w-3 mr-1 text-red-500" />}
                {card.change}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Detailed Metrics */}
      {data && (
        <div className="grid gap-4 md:grid-cols-3">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Cost Metrics</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Cost per Click</span>
                <span className="text-lg font-semibold">{formatCurrency(data.metrics.cpc)}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Cost per Mille</span>
                <span className="text-lg font-semibold">{formatCurrency(data.metrics.cpm)}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Total Spent</span>
                <span className="text-lg font-semibold">{formatCurrency(data.metrics.spent)}</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Engagement Metrics</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Total Reach</span>
                <span className="text-lg font-semibold">{formatNumber(data.metrics.reach)}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Engagement Rate</span>
                <span className="text-lg font-semibold">
                  {((data.metrics.engagement / data.metrics.impressions) * 100).toFixed(2)}%
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Conversions</span>
                <span className="text-lg font-semibold">{data.metrics.conversions}</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Performance Trends</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Impressions</span>
                <div className="flex items-center space-x-2">
                  {data.trends.impressions >= 0 ? (
                    <TrendingUp className="h-4 w-4 text-green-600" />
                  ) : (
                    <TrendingDown className="h-4 w-4 text-red-600" />
                  )}
                  <span className={data.trends.impressions >= 0 ? "text-green-600" : "text-red-600"}>
                    {data.trends.impressions > 0 ? "+" : ""}
                    {data.trends.impressions}%
                  </span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Engagement</span>
                <div className="flex items-center space-x-2">
                  {data.trends.engagement >= 0 ? (
                    <TrendingUp className="h-4 w-4 text-green-600" />
                  ) : (
                    <TrendingDown className="h-4 w-4 text-red-600" />
                  )}
                  <span className={data.trends.engagement >= 0 ? "text-green-600" : "text-red-600"}>
                    {data.trends.engagement > 0 ? "+" : ""}
                    {data.trends.engagement}%
                  </span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Clicks</span>
                <div className="flex items-center space-x-2">
                  {data.trends.clicks >= 0 ? (
                    <TrendingUp className="h-4 w-4 text-green-600" />
                  ) : (
                    <TrendingDown className="h-4 w-4 text-red-600" />
                  )}
                  <span className={data.trends.clicks >= 0 ? "text-green-600" : "text-red-600"}>
                    {data.trends.clicks > 0 ? "+" : ""}
                    {data.trends.clicks}%
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Top Performers and Campaign Breakdown */}
      {data && (
        <div className="grid gap-4 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Top Performing Content</CardTitle>
              <CardDescription>Your best performing posts across all platforms</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {data.topPerformers.map((post, index) => (
                  <div key={post.id} className="flex items-center space-x-4">
                    <div className="flex-shrink-0 w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                      <span className="text-sm font-semibold text-primary">#{index + 1}</span>
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">{post.title}</p>
                      <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                        <Badge variant="outline" className="capitalize">
                          {post.platform}
                        </Badge>
                        <span>{formatNumber(post.impressions)} impressions</span>
                      </div>
                    </div>
                    <div className="flex-shrink-0 text-right">
                      <div className="text-sm font-semibold">{formatNumber(post.engagement)}</div>
                      <div className="text-xs text-muted-foreground">engagements</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Campaign Performance</CardTitle>
              <CardDescription>Budget utilization and performance by campaign</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {data.campaignBreakdown.map((campaign) => (
                  <div key={campaign.id} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <span className="text-sm font-medium">{campaign.name}</span>
                        <Badge variant={campaign.status === "active" ? "default" : "secondary"} className="text-xs">
                          {campaign.status}
                        </Badge>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-semibold">
                          {formatCurrency(campaign.spent)} / {formatCurrency(campaign.budget)}
                        </div>
                        <div className="text-xs text-muted-foreground">{campaign.performance}% performance</div>
                      </div>
                    </div>
                    <Progress value={(campaign.spent / campaign.budget) * 100} className="h-2" />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}

"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { formatCurrency, formatNumber } from "@/lib/utils"
import { TrendingUp, TrendingDown, Play, Pause, Settings } from "lucide-react"
import Link from "next/link"
import type { CampaignPerformance as CampaignPerformanceType } from "@/lib/types"

export function CampaignPerformance() {
  const [campaigns, setCampaigns] = useState<CampaignPerformanceType[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    async function fetchCampaigns() {
      try {
        const response = await fetch("/api/dashboard/campaign-performance")
        if (response.ok) {
          const data = await response.json()
          setCampaigns(data.campaigns)
        }
      } catch (error) {
        console.error("Failed to fetch campaign performance:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchCampaigns()
  }, [])

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Campaign Performance</CardTitle>
          <CardDescription>Overview of your active campaigns</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="h-4 w-32 bg-muted animate-pulse rounded" />
                  <div className="h-4 w-16 bg-muted animate-pulse rounded" />
                </div>
                <div className="h-2 w-full bg-muted animate-pulse rounded" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800"
      case "paused":
        return "bg-yellow-100 text-yellow-800"
      case "completed":
        return "bg-blue-100 text-blue-800"
      case "draft":
        return "bg-gray-100 text-gray-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getPerformanceColor = (roas: number) => {
    if (roas >= 3) return "text-green-600"
    if (roas >= 2) return "text-yellow-600"
    return "text-red-600"
  }

  const getPerformanceIcon = (roas: number) => {
    return roas >= 2 ? (
      <TrendingUp className="h-4 w-4 text-green-600" />
    ) : (
      <TrendingDown className="h-4 w-4 text-red-600" />
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Campaign Performance</CardTitle>
        <CardDescription>Overview of your active campaigns</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {campaigns.length === 0 ? (
            <div className="text-center py-8">
              <div className="text-muted-foreground mb-4">
                <p>No campaigns found. Create your first campaign to see performance data.</p>
              </div>
              <Button asChild>
                <Link href="/campaigns/wizard">Create Campaign</Link>
              </Button>
            </div>
          ) : (
            campaigns.map((campaign) => (
              <div key={campaign.id} className="border rounded-lg p-4 space-y-4">
                {/* Campaign Header */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <h3 className="font-semibold">{campaign.name}</h3>
                    <Badge className={getStatusColor(campaign.status)}>{campaign.status}</Badge>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button variant="ghost" size="sm" asChild>
                      <Link href={`/campaigns/${campaign.id}`}>
                        <Settings className="h-4 w-4" />
                      </Link>
                    </Button>
                    {campaign.status === "active" ? (
                      <Button variant="ghost" size="sm">
                        <Pause className="h-4 w-4" />
                      </Button>
                    ) : (
                      <Button variant="ghost" size="sm">
                        <Play className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>

                {/* Budget Progress */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>Budget Usage</span>
                    <span>
                      {formatCurrency(campaign.spent)} / {formatCurrency(campaign.budget)}
                    </span>
                  </div>
                  <Progress value={(campaign.spent / campaign.budget) * 100} className="h-2" />
                </div>

                {/* Metrics Grid */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                  <div className="space-y-1">
                    <div className="text-2xl font-bold">{formatNumber(campaign.impressions)}</div>
                    <div className="text-xs text-muted-foreground">Impressions</div>
                  </div>
                  <div className="space-y-1">
                    <div className="text-2xl font-bold">{formatNumber(campaign.clicks)}</div>
                    <div className="text-xs text-muted-foreground">Clicks</div>
                  </div>
                  <div className="space-y-1">
                    <div className="text-2xl font-bold">{campaign.ctr}%</div>
                    <div className="text-xs text-muted-foreground">CTR</div>
                  </div>
                  <div className="space-y-1">
                    <div
                      className={`text-2xl font-bold flex items-center justify-center space-x-1 ${getPerformanceColor(campaign.roas)}`}
                    >
                      {getPerformanceIcon(campaign.roas)}
                      <span>{campaign.roas}x</span>
                    </div>
                    <div className="text-xs text-muted-foreground">ROAS</div>
                  </div>
                </div>

                {/* Additional Metrics */}
                <div className="flex items-center justify-between text-sm text-muted-foreground border-t pt-3">
                  <div className="flex items-center space-x-4">
                    <span>CPC: {formatCurrency(campaign.cpc)}</span>
                    <span>Conversions: {campaign.conversions}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span>
                      {campaign.startDate && new Date(campaign.startDate).toLocaleDateString()} -{" "}
                      {campaign.endDate ? new Date(campaign.endDate).toLocaleDateString() : "Ongoing"}
                    </span>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  )
}

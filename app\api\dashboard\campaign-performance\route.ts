import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.tenantId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const period = searchParams.get("period") || "30d"

    const now = new Date()
    const daysBack = period === "7d" ? 7 : period === "90d" ? 90 : 30
    const startDate = new Date(now.getTime() - daysBack * 24 * 60 * 60 * 1000)

    const tenantId = session.user.tenantId

    // Get campaigns with their analytics
    const campaignsWithAnalytics = await prisma.campaign.findMany({
      where: {
        tenantId,
        createdAt: { gte: startDate },
      },
      include: {
        analyticsEvents: {
          where: {
            createdAt: { gte: startDate },
          },
        },
        posts: {
          where: {
            publishedAt: { gte: startDate },
          },
        },
      },
      orderBy: { createdAt: "desc" },
    })

    // Calculate performance metrics for each campaign
    const campaigns = campaignsWithAnalytics.map((campaign) => {
      const analytics = campaign.analyticsEvents.reduce(
        (acc, event) => {
          acc[event.eventType] = (acc[event.eventType] || 0) + event.value
          return acc
        },
        {} as Record<string, number>,
      )

      const impressions = analytics.impression || 0
      const clicks = analytics.click || 0
      const conversions = analytics.conversion || 0
      const revenue = analytics.revenue || 0

      // Calculate ROI (Return on Investment)
      const spent = Number(campaign.spent)
      const roi = spent > 0 ? revenue / spent : 0

      // Calculate performance score based on multiple factors
      const budget = Number(campaign.budget)
      const budgetUtilization = budget > 0 ? (spent / budget) * 100 : 0
      const ctr = impressions > 0 ? (clicks / impressions) * 100 : 0
      const conversionRate = clicks > 0 ? (conversions / clicks) * 100 : 0

      // Weighted performance score
      const performance = Math.min(100, ctr * 0.3 + conversionRate * 0.4 + roi * 10 * 0.3)

      return {
        id: campaign.id,
        name: campaign.name,
        spent,
        budget,
        performance: Math.round(performance),
        status: campaign.status,
        roi,
        posts: campaign.posts.length,
        impressions,
        clicks,
        conversions,
      }
    })

    // Sort by performance score
    const sortedCampaigns = campaigns.sort((a, b) => b.performance - a.performance)

    return NextResponse.json({ campaigns: sortedCampaigns })
  } catch (error) {
    console.error("Failed to fetch campaign performance:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

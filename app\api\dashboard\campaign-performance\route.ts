import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const tenantId = session.user.tenantId

    const campaigns = await prisma.campaign.findMany({
      where: { tenantId },
      include: {
        analyticsEvents: {
          select: {
            eventType: true,
            value: true,
          },
        },
        posts: {
          select: {
            id: true,
          },
        },
      },
      orderBy: { createdAt: "desc" },
      take: 10,
    })

    const campaignPerformance = campaigns.map((campaign) => {
      const impressions = campaign.analyticsEvents
        .filter((e) => e.eventType === "impression")
        .reduce((sum, e) => sum + e.value, 0)

      const clicks = campaign.analyticsEvents
        .filter((e) => e.eventType === "click")
        .reduce((sum, e) => sum + e.value, 0)

      const conversions = campaign.analyticsEvents
        .filter((e) => e.eventType === "conversion")
        .reduce((sum, e) => sum + e.value, 0)

      const ctr = impressions > 0 ? (clicks / impressions) * 100 : 0
      const cpc = clicks > 0 ? campaign.spent / clicks : 0
      const roas = campaign.spent > 0 ? (conversions * 10) / campaign.spent : 0

      return {
        id: campaign.id,
        name: campaign.name,
        status: campaign.status,
        budget: campaign.budget,
        spent: campaign.spent,
        impressions,
        clicks,
        conversions,
        ctr: Math.round(ctr * 100) / 100,
        cpc: Math.round(cpc * 100) / 100,
        roas: Math.round(roas * 100) / 100,
        startDate: campaign.startDate,
        endDate: campaign.endDate,
      }
    })

    return NextResponse.json({ campaigns: campaignPerformance })
  } catch (error) {
    console.error("Failed to fetch campaign performance:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

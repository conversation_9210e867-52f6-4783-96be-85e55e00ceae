import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const tenantId = session.user.tenantId

    // Get basic counts
    const [totalCampaigns, activeCampaigns, totalPosts, publishedPosts] = await Promise.all([
      prisma.campaign.count({ where: { tenantId } }),
      prisma.campaign.count({ where: { tenantId, status: "active" } }),
      prisma.post.count({ where: { tenantId } }),
      prisma.post.count({ where: { tenantId, status: "published" } }),
    ])

    // Get analytics aggregations
    const analyticsAgg = await prisma.analyticsEvent.aggregate({
      where: { tenantId },
      _sum: {
        value: true,
      },
      _count: {
        id: true,
      },
    })

    // Get engagement metrics by type
    const engagementMetrics = await prisma.analyticsEvent.groupBy({
      by: ["eventType"],
      where: { tenantId },
      _sum: {
        value: true,
      },
    })

    const totalEngagement = engagementMetrics
      .filter((m) => ["like", "comment", "share", "reaction"].includes(m.eventType))
      .reduce((sum, m) => sum + (m._sum.value || 0), 0)

    const totalReach = engagementMetrics
      .filter((m) => m.eventType === "reach")
      .reduce((sum, m) => sum + (m._sum.value || 0), 0)

    const totalImpressions = engagementMetrics
      .filter((m) => m.eventType === "impression")
      .reduce((sum, m) => sum + (m._sum.value || 0), 0)

    const totalClicks = engagementMetrics
      .filter((m) => m.eventType === "click")
      .reduce((sum, m) => sum + (m._sum.value || 0), 0)

    // Calculate rates
    const engagementRate = totalImpressions > 0 ? (totalEngagement / totalImpressions) * 100 : 0
    const clickThroughRate = totalImpressions > 0 ? (totalClicks / totalImpressions) * 100 : 0

    // Get campaign spending
    const campaignSpending = await prisma.campaign.aggregate({
      where: { tenantId },
      _sum: {
        spent: true,
      },
    })

    const totalSpent = campaignSpending._sum.spent || 0
    const costPerClick = totalClicks > 0 ? totalSpent / totalClicks : 0
    const returnOnAdSpend = totalSpent > 0 ? (totalEngagement * 0.1) / totalSpent : 0

    const metrics = {
      totalCampaigns,
      activeCampaigns,
      totalPosts,
      publishedPosts,
      totalEngagement,
      totalReach,
      totalImpressions,
      totalClicks,
      engagementRate: Math.round(engagementRate * 100) / 100,
      clickThroughRate: Math.round(clickThroughRate * 100) / 100,
      costPerClick: Math.round(costPerClick * 100) / 100,
      returnOnAdSpend: Math.round(returnOnAdSpend * 100) / 100,
    }

    return NextResponse.json({ metrics })
  } catch (error) {
    console.error("Failed to fetch dashboard metrics:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.tenantId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const period = searchParams.get("period") || "30d"

    // Calculate date range
    const now = new Date()
    const daysBack = period === "7d" ? 7 : period === "90d" ? 90 : 30
    const startDate = new Date(now.getTime() - daysBack * 24 * 60 * 60 * 1000)

    const tenantId = session.user.tenantId

    // Get campaign metrics
    const [totalCampaigns, activeCampaigns, campaignBudgets, totalPosts, publishedPosts, analyticsData] =
      await Promise.all([
        prisma.campaign.count({ where: { tenantId } }),
        prisma.campaign.count({ where: { tenantId, status: "active" } }),
        prisma.campaign.aggregate({
          where: { tenantId },
          _sum: { budget: true, spent: true },
        }),
        prisma.post.count({ where: { tenantId } }),
        prisma.post.count({ where: { tenantId, status: "published" } }),
        prisma.analyticsEvent.groupBy({
          by: ["eventType"],
          where: {
            tenantId,
            createdAt: { gte: startDate },
          },
          _sum: { value: true },
        }),
      ])

    // Process analytics data
    const analyticsMap = analyticsData.reduce(
      (acc, item) => {
        acc[item.eventType] = item._sum.value || 0
        return acc
      },
      {} as Record<string, number>,
    )

    const impressions = analyticsMap.impression || 0
    const engagement = analyticsMap.like + analyticsMap.comment + analyticsMap.share + analyticsMap.reaction || 0
    const reach = analyticsMap.reach || 0
    const clicks = analyticsMap.click || 0
    const conversions = analyticsMap.conversion || 0

    const engagementRate = impressions > 0 ? (engagement / impressions) * 100 : 0
    const clickThroughRate = impressions > 0 ? (clicks / impressions) * 100 : 0
    const costPerClick = clicks > 0 ? (campaignBudgets._sum.spent || 0) / clicks : 0

    const metrics = {
      totalCampaigns,
      activeCampaigns,
      publishedPosts,
      totalPosts,
      totalEngagement: engagement,
      engagementRate,
      clickThroughRate,
      costPerClick,
      totalSpent: campaignBudgets._sum.spent || 0,
      totalBudget: campaignBudgets._sum.budget || 0,
      impressions,
      reach,
      clicks,
      conversions,
    }

    return NextResponse.json({ metrics })
  } catch (error) {
    console.error("Failed to fetch dashboard metrics:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

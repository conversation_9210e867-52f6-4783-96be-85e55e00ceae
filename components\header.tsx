"use client"

import { useSession } from "next-auth/react"
import { Search, Plus } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { UserNav } from "@/components/layout/user-nav"
import { NotificationDropdown } from "@/components/layout/notification-dropdown"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import Link from "next/link"

export function Header() {
  const { data: session } = useSession()

  return (
    <header className="flex items-center justify-between px-6 py-4 bg-background border-b border-border">
      {/* Search */}
      <div className="flex items-center space-x-4 flex-1 max-w-md">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input placeholder="Search campaigns, posts, analytics..." className="pl-10 bg-muted/50" />
        </div>
      </div>

      {/* Actions */}
      <div className="flex items-center space-x-4">
        {/* Quick Create */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button size="sm">
              <Plus className="h-4 w-4 mr-2" />
              Create
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            <DropdownMenuItem asChild>
              <Link href="/campaigns/wizard">New Campaign</Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link href="/posts/new">New Post</Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link href="/ai-studio">AI Generate</Link>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Notifications */}
        <NotificationDropdown />

        {/* User Menu */}
        <UserNav />
      </div>
    </header>
  )
}

import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.tenantId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { providerType, apiKey } = await request.json()

    if (!providerType || !apiKey) {
      return NextResponse.json({ error: "Provider type and API key required" }, { status: 400 })
    }

    // Test the API key based on provider type
    let isValid = false

    try {
      switch (providerType) {
        case "openai":
          const openaiResponse = await fetch("https://api.openai.com/v1/models", {
            headers: { Authorization: `Bearer ${apiKey}` },
          })
          isValid = openaiResponse.ok
          break

        case "anthropic":
          const anthropicResponse = await fetch("https://api.anthropic.com/v1/messages", {
            method: "POST",
            headers: {
              "x-api-key": apiKey,
              "Content-Type": "application/json",
              "anthropic-version": "2023-06-01",
            },
            body: JSON.stringify({
              model: "claude-3-haiku-20240307",
              max_tokens: 1,
              messages: [{ role: "user", content: "test" }],
            }),
          })
          isValid = anthropicResponse.status !== 401
          break

        case "google":
          const googleResponse = await fetch(`https://generativelanguage.googleapis.com/v1beta/models?key=${apiKey}`)
          isValid = googleResponse.ok
          break

        case "groq":
          const groqResponse = await fetch("https://api.groq.com/openai/v1/models", {
            headers: { Authorization: `Bearer ${apiKey}` },
          })
          isValid = groqResponse.ok
          break

        case "xai":
          const xaiResponse = await fetch("https://api.x.ai/v1/models", {
            headers: { Authorization: `Bearer ${apiKey}` },
          })
          isValid = xaiResponse.ok
          break

        case "deepseek":
          const deepseekResponse = await fetch("https://api.deepseek.com/v1/models", {
            headers: { Authorization: `Bearer ${apiKey}` },
          })
          isValid = deepseekResponse.ok
          break

        case "openrouter":
          const openrouterResponse = await fetch("https://openrouter.ai/api/v1/models", {
            headers: { Authorization: `Bearer ${apiKey}` },
          })
          isValid = openrouterResponse.ok
          break

        default:
          return NextResponse.json({ error: "Unsupported provider type" }, { status: 400 })
      }
    } catch (error) {
      console.error(`API test failed for ${providerType}:`, error)
      isValid = false
    }

    return NextResponse.json({ valid: isValid })
  } catch (error) {
    console.error("API key test error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

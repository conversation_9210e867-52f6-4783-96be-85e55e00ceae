export interface User {
  id: string
  email: string
  name: string
  image?: string
  role: string
  tenantId: string
  tenantName?: string
  createdAt: Date
  updatedAt: Date
}

export interface Tenant {
  id: string
  name: string
  domain: string
  settings: Record<string, any>
  subscriptionTier: string
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

export interface Campaign {
  id: string
  tenantId: string
  userId: string
  name: string
  description?: string
  campaignType: string
  status: string
  budget: number
  spent: number
  startDate: Date
  endDate?: Date
  targetAudience: Record<string, any>
  settings: Record<string, any>
  metrics: Record<string, any>
  createdAt: Date
  updatedAt: Date
}

export interface Post {
  id: string
  tenantId: string
  userId: string
  campaignId?: string
  title?: string
  content: string
  mediaUrls: string[]
  hashtags: string[]
  platforms: string[]
  status: string
  scheduledAt?: Date
  publishedAt?: Date
  aiGenerated: boolean
  aiModelUsed?: string
  metrics: Record<string, any>
  createdAt: Date
  updatedAt: Date
}

export interface SocialAccount {
  id: string
  tenantId: string
  userId: string
  platform: string
  accountId: string
  accountName: string
  accessTokenEncrypted: string
  refreshTokenEncrypted?: string
  expiresAt?: Date
  permissions: string[]
  isActive: boolean
  healthStatus: string
  lastHealthCheck: Date
  createdAt: Date
  updatedAt: Date
}

export interface AIProvider {
  id: string
  tenantId: string
  userId: string
  name: string
  providerType: string
  apiKeyEncrypted: string
  baseUrl?: string
  models: string[]
  isActive: boolean
  healthStatus: string
  lastHealthCheck?: Date
  settings: Record<string, any>
  createdAt: Date
  updatedAt: Date
}

export interface AIUsage {
  id: string
  tenantId: string
  userId: string
  provider: string
  model: string
  tokensUsed: number
  cost: number
  requestType: string
  createdAt: Date
}

export interface AnalyticsEvent {
  id: string
  tenantId: string
  postId?: string
  campaignId?: string
  eventType: string
  platform: string
  value: number
  metadata: Record<string, any>
  createdAt: Date
}

export interface JobQueue {
  id: string
  tenantId: string
  type: string
  payload: Record<string, any>
  status: string
  attempts: number
  maxAttempts: number
  scheduledFor: Date
  processedAt?: Date
  error?: string
  createdAt: Date
  updatedAt: Date
}

export interface Notification {
  id: string
  tenantId: string
  userId: string
  type: string
  title: string
  message: string
  data: Record<string, any>
  read: boolean
  createdAt: Date
}

export interface TeamMember {
  id: string
  tenantId: string
  userId: string
  role: string
  permissions: string[]
  invitedBy?: string
  joinedAt: Date
  createdAt: Date
}

export interface FileUpload {
  id: string
  tenantId: string
  userId: string
  filename: string
  originalName: string
  mimeType: string
  size: number
  url: string
  metadata: Record<string, any>
  createdAt: Date
}

export interface DashboardMetrics {
  totalCampaigns: number
  activeCampaigns: number
  totalPosts: number
  publishedPosts: number
  totalEngagement: number
  totalReach: number
  totalImpressions: number
  totalClicks: number
  engagementRate: number
  clickThroughRate: number
  costPerClick: number
  returnOnAdSpend: number
}

export interface ChartData {
  date: string
  value: number
  label?: string
  category?: string
}

export interface PlatformMetrics {
  platform: string
  posts: number
  engagement: number
  reach: number
  impressions: number
  clicks: number
  followers: number
  growth: number
}

export interface TopPost {
  id: string
  title: string
  content: string
  platform: string
  engagement: number
  reach: number
  publishedAt: Date
  metrics: Record<string, any>
}

export interface RecentActivity {
  id: string
  type: string
  title: string
  description: string
  timestamp: Date
  user: string
  metadata?: Record<string, any>
}

export interface CampaignPerformance {
  id: string
  name: string
  status: string
  budget: number
  spent: number
  impressions: number
  clicks: number
  conversions: number
  ctr: number
  cpc: number
  roas: number
  startDate: Date
  endDate?: Date
}

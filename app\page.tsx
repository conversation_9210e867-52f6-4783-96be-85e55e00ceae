import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { redirect } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Bot, Zap, Target, BarChart3, Users, Shield, ArrowRight, CheckCircle } from "lucide-react"
import Link from "next/link"

export default async function HomePage() {
  const session = await getServerSession(authOptions)

  if (session?.user) {
    redirect("/dashboard")
  }

  const features = [
    {
      icon: Bot,
      title: "AI-Powered Content",
      description: "Generate engaging posts with advanced AI models including GPT-4, Claude, and Gemini",
      color: "text-blue-600",
    },
    {
      icon: Target,
      title: "Smart Campaigns",
      description: "Create targeted marketing campaigns with intelligent audience segmentation",
      color: "text-green-600",
    },
    {
      icon: BarChart3,
      title: "Advanced Analytics",
      description: "Track performance with real-time insights and comprehensive reporting",
      color: "text-purple-600",
    },
    {
      icon: Zap,
      title: "Multi-Platform Publishing",
      description: "Publish to Facebook, Instagram, Twitter, LinkedIn, YouTube, and TikTok",
      color: "text-orange-600",
    },
    {
      icon: Users,
      title: "Team Collaboration",
      description: "Work together with role-based permissions and approval workflows",
      color: "text-pink-600",
    },
    {
      icon: Shield,
      title: "Enterprise Security",
      description: "Bank-level encryption and compliance with SOC 2 and GDPR standards",
      color: "text-indigo-600",
    },
  ]

  const benefits = [
    "Save 10+ hours per week on content creation",
    "Increase engagement rates by up to 300%",
    "Manage unlimited social media accounts",
    "AI-powered content optimization",
    "Real-time performance monitoring",
    "24/7 customer support",
    "99.9% uptime guarantee",
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      {/* Header */}
      <header className="border-b border-border/40 backdrop-blur-sm bg-background/80 sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                <Bot className="h-5 w-5 text-primary-foreground" />
              </div>
              <div>
                <h1 className="text-xl font-bold">AI Marketing Platform</h1>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Button variant="ghost" asChild>
                <Link href="/auth/signin">Sign In</Link>
              </Button>
              <Button asChild>
                <Link href="/auth/signup">Get Started</Link>
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 px-4">
        <div className="container mx-auto text-center">
          <Badge variant="secondary" className="mb-4">
            🚀 Now with GPT-4 and Claude 3.5 Sonnet
          </Badge>
          <h1 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
            AI-Powered Marketing
            <br />
            That Actually Works
          </h1>
          <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
            Create, schedule, and optimize your social media content with advanced AI. Increase engagement, save time,
            and grow your audience across all platforms.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" asChild>
              <Link href="/auth/signup">
                Start Free Trial
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
            <Button size="lg" variant="outline" asChild>
              <Link href="/demo">Watch Demo</Link>
            </Button>
          </div>
          <p className="text-sm text-muted-foreground mt-4">
            No credit card required • 14-day free trial • Cancel anytime
          </p>
        </div>
      </section>

      {/* Features Grid */}
      <section className="py-20 px-4 bg-muted/30">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Everything You Need to Succeed</h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Powerful features designed to streamline your marketing workflow and maximize results
            </p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="border-0 shadow-lg hover:shadow-xl transition-shadow">
                <CardHeader>
                  <div className={`w-12 h-12 rounded-lg bg-muted flex items-center justify-center mb-4`}>
                    <feature.icon className={`h-6 w-6 ${feature.color}`} />
                  </div>
                  <CardTitle className="text-xl">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base">{feature.description}</CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 px-4">
        <div className="container mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold mb-6">Join 10,000+ Marketers Who Trust Our Platform</h2>
              <p className="text-lg text-muted-foreground mb-8">
                Our AI-powered platform helps businesses of all sizes create better content, engage their audience, and
                drive real results.
              </p>
              <div className="space-y-4">
                {benefits.map((benefit, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <CheckCircle className="h-5 w-5 text-green-600 flex-shrink-0" />
                    <span className="text-muted-foreground">{benefit}</span>
                  </div>
                ))}
              </div>
            </div>
            <div className="relative">
              <div className="aspect-video bg-gradient-to-br from-primary/20 to-primary/5 rounded-2xl flex items-center justify-center">
                <div className="text-center">
                  <BarChart3 className="h-24 w-24 text-primary mx-auto mb-4" />
                  <p className="text-lg font-semibold">Interactive Demo</p>
                  <p className="text-muted-foreground">See the platform in action</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 bg-primary text-primary-foreground">
        <div className="container mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Ready to Transform Your Marketing?</h2>
          <p className="text-xl opacity-90 mb-8 max-w-2xl mx-auto">
            Join thousands of successful marketers who use our AI platform to create engaging content and grow their
            audience.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" variant="secondary" asChild>
              <Link href="/auth/signup">
                Start Your Free Trial
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="bg-transparent border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary"
              asChild
            >
              <Link href="/contact">Contact Sales</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-12 px-4 border-t border-border">
        <div className="container mx-auto">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                  <Bot className="h-5 w-5 text-primary-foreground" />
                </div>
                <span className="font-bold">AI Marketing Platform</span>
              </div>
              <p className="text-muted-foreground">
                The most advanced AI-powered marketing platform for modern businesses.
              </p>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Product</h4>
              <div className="space-y-2 text-muted-foreground">
                <Link href="/features" className="block hover:text-foreground">
                  Features
                </Link>
                <Link href="/pricing" className="block hover:text-foreground">
                  Pricing
                </Link>
                <Link href="/integrations" className="block hover:text-foreground">
                  Integrations
                </Link>
                <Link href="/api" className="block hover:text-foreground">
                  API
                </Link>
              </div>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Company</h4>
              <div className="space-y-2 text-muted-foreground">
                <Link href="/about" className="block hover:text-foreground">
                  About
                </Link>
                <Link href="/blog" className="block hover:text-foreground">
                  Blog
                </Link>
                <Link href="/careers" className="block hover:text-foreground">
                  Careers
                </Link>
                <Link href="/contact" className="block hover:text-foreground">
                  Contact
                </Link>
              </div>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Support</h4>
              <div className="space-y-2 text-muted-foreground">
                <Link href="/help" className="block hover:text-foreground">
                  Help Center
                </Link>
                <Link href="/docs" className="block hover:text-foreground">
                  Documentation
                </Link>
                <Link href="/status" className="block hover:text-foreground">
                  Status
                </Link>
                <Link href="/privacy" className="block hover:text-foreground">
                  Privacy
                </Link>
              </div>
            </div>
          </div>
          <div className="border-t border-border mt-8 pt-8 text-center text-muted-foreground">
            <p>&copy; 2024 AI Marketing Platform. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}

import { PrismaClient } from "@prisma/client"

const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined
}

export const prisma = globalForPrisma.prisma ?? new PrismaClient()

if (process.env.NODE_ENV !== "production") globalForPrisma.prisma = prisma

// Connection health check
export async function checkDatabaseConnection() {
  try {
    await prisma.$queryRaw`SELECT 1`
    return { status: "healthy", message: "Database connection successful" }
  } catch (error) {
    console.error("Database connection failed:", error)
    return { status: "error", message: "Database connection failed" }
  }
}

// Graceful shutdown
process.on("beforeExit", async () => {
  await prisma.$disconnect()
})

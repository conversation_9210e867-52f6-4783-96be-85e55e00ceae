import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { FacebookProvider, TwitterProvider, LinkedInProvider } from "@/lib/social-media"

export async function POST(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.tenantId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Get the social account
    const account = await prisma.socialAccount.findFirst({
      where: {
        id: params.id,
        tenantId: session.user.tenantId,
      },
    })

    if (!account) {
      return NextResponse.json({ error: "Account not found" }, { status: 404 })
    }

    // Create provider instance and refresh token
    let provider
    switch (account.platform) {
      case "facebook":
        provider = new FacebookProvider(account)
        break
      case "twitter":
        provider = new TwitterProvider(account)
        break
      case "linkedin":
        provider = new LinkedInProvider(account)
        break
      default:
        return NextResponse.json({ error: "Unsupported platform" }, { status: 400 })
    }

    const refreshed = await provider.refreshToken()

    if (refreshed) {
      return NextResponse.json({ message: "Token refreshed successfully" })
    } else {
      return NextResponse.json({ error: "Failed to refresh token" }, { status: 400 })
    }
  } catch (error) {
    console.error("Failed to refresh social account token:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

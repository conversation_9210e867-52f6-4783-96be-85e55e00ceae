"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import {
  ArrowLeft,
  ArrowRight,
  CheckCircle,
  ExternalLink,
  Sparkles,
  Zap,
  Brain,
  Rocket,
  Eye,
  EyeOff,
} from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { SUPPORTED_PROVIDERS } from "@/lib/ai-providers-enhanced"

export default function AIProviderSetupWizard() {
  const router = useRouter()
  const { toast } = useToast()

  const [currentStep, setCurrentStep] = useState(0)
  const [selectedProviders, setSelectedProviders] = useState<string[]>([])
  const [apiKeys, setApiKeys] = useState<Record<string, string>>({})
  const [showApiKey, setShowApiKey] = useState<Record<string, boolean>>({})
  const [testing, setTesting] = useState<Record<string, boolean>>({})
  const [testResults, setTestResults] = useState<Record<string, boolean>>({})
  const [loading, setLoading] = useState(false)
  const [helpDialogOpen, setHelpDialogOpen] = useState(false)
  const [selectedProviderHelp, setSelectedProviderHelp] = useState<string>("")

  const steps = [
    {
      title: "Welcome to AI Setup",
      description: "Let's connect your AI providers in 3 easy steps",
    },
    {
      title: "Choose AI Providers",
      description: "Select the AI providers you want to use",
    },
    {
      title: "Connect Your APIs",
      description: "Add your API keys to connect the providers",
    },
    {
      title: "Test & Complete",
      description: "We'll test your connections and you're ready to go!",
    },
  ]

  const recommendedProviders = Object.entries(SUPPORTED_PROVIDERS)
    .filter(([_, config]) => config.recommended)
    .map(([key]) => key)

  async function testApiKey(providerKey: string, apiKey: string) {
    setTesting((prev) => ({ ...prev, [providerKey]: true }))

    try {
      const response = await fetch("/api/ai-providers/test", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          providerType: providerKey,
          apiKey: apiKey,
        }),
      })

      const isValid = response.ok
      setTestResults((prev) => ({ ...prev, [providerKey]: isValid }))

      if (isValid) {
        toast({
          title: "Connection Successful! ✅",
          description: `${SUPPORTED_PROVIDERS[providerKey as keyof typeof SUPPORTED_PROVIDERS].name} is ready to use`,
        })
      } else {
        toast({
          title: "Connection Failed ❌",
          description: "Please check your API key and try again",
          variant: "destructive",
        })
      }
    } catch (error) {
      setTestResults((prev) => ({ ...prev, [providerKey]: false }))
      toast({
        title: "Connection Error",
        description: "Unable to test connection. Please try again.",
        variant: "destructive",
      })
    } finally {
      setTesting((prev) => ({ ...prev, [providerKey]: false }))
    }
  }

  async function completeSetup() {
    setLoading(true)

    try {
      const setupPromises = selectedProviders.map(async (providerKey) => {
        const config = SUPPORTED_PROVIDERS[providerKey as keyof typeof SUPPORTED_PROVIDERS]
        const apiKey = apiKeys[providerKey]

        if (!apiKey) return null

        return fetch("/api/ai-providers", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            name: `${config.name} (Auto-configured)`,
            providerType: providerKey,
            apiKey: apiKey,
            baseUrl: config.baseUrl,
            models: config.models,
          }),
        })
      })

      await Promise.all(setupPromises)

      toast({
        title: "🎉 Setup Complete!",
        description: "Your AI providers are ready. Let's create some amazing content!",
      })

      router.push("/ai-studio")
    } catch (error) {
      toast({
        title: "Setup Error",
        description: "Some providers couldn't be configured. You can add them later.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  function openHelp(providerKey: string) {
    setSelectedProviderHelp(providerKey)
    setHelpDialogOpen(true)
  }

  const progress = ((currentStep + 1) / steps.length) * 100

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <Sparkles className="h-8 w-8 text-blue-600" />
            <h1 className="text-3xl font-bold">AI Provider Setup</h1>
          </div>
          <Progress value={progress} className="w-full max-w-md mx-auto" />
          <p className="text-muted-foreground mt-2">
            Step {currentStep + 1} of {steps.length}: {steps[currentStep].title}
          </p>
        </div>

        {/* Step Content */}
        <Card className="mb-8">
          <CardHeader className="text-center">
            <CardTitle className="flex items-center justify-center space-x-2">
              {currentStep === 0 && <Brain className="h-6 w-6 text-blue-600" />}
              {currentStep === 1 && <Zap className="h-6 w-6 text-green-600" />}
              {currentStep === 2 && <Rocket className="h-6 w-6 text-purple-600" />}
              {currentStep === 3 && <CheckCircle className="h-6 w-6 text-emerald-600" />}
              <span>{steps[currentStep].title}</span>
            </CardTitle>
            <CardDescription>{steps[currentStep].description}</CardDescription>
          </CardHeader>
          <CardContent>
            {/* Step 0: Welcome */}
            {currentStep === 0 && (
              <div className="text-center space-y-6">
                <div className="grid md:grid-cols-3 gap-4">
                  <div className="p-4 rounded-lg bg-blue-50">
                    <Brain className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                    <h3 className="font-semibold">Multiple AI Models</h3>
                    <p className="text-sm text-muted-foreground">Access GPT, Claude, Gemini & more</p>
                  </div>
                  <div className="p-4 rounded-lg bg-green-50">
                    <Zap className="h-8 w-8 text-green-600 mx-auto mb-2" />
                    <h3 className="font-semibold">Lightning Fast</h3>
                    <p className="text-sm text-muted-foreground">Generate content in seconds</p>
                  </div>
                  <div className="p-4 rounded-lg bg-purple-50">
                    <Rocket className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                    <h3 className="font-semibold">Smart Optimization</h3>
                    <p className="text-sm text-muted-foreground">AI picks the best model for each task</p>
                  </div>
                </div>
                <Alert>
                  <Sparkles className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Pro Tip:</strong> Start with OpenAI or Claude for the best experience. You can always add
                    more providers later!
                  </AlertDescription>
                </Alert>
              </div>
            )}

            {/* Step 1: Provider Selection */}
            {currentStep === 1 && (
              <div className="space-y-6">
                <div className="text-center mb-6">
                  <h3 className="text-lg font-semibold mb-2">Recommended Providers</h3>
                  <p className="text-muted-foreground">
                    These providers offer the best experience for marketing content
                  </p>
                </div>

                <div className="grid md:grid-cols-2 gap-4">
                  {Object.entries(SUPPORTED_PROVIDERS).map(([key, config]) => (
                    <Card
                      key={key}
                      className={`cursor-pointer transition-all hover:shadow-md ${
                        selectedProviders.includes(key) ? "ring-2 ring-blue-500 bg-blue-50" : ""
                      }`}
                      onClick={() => {
                        setSelectedProviders((prev) =>
                          prev.includes(key) ? prev.filter((p) => p !== key) : [...prev, key],
                        )
                      }}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="text-2xl">{config.icon}</div>
                            <div>
                              <h4 className="font-semibold flex items-center space-x-2">
                                <span>{config.name}</span>
                                {config.recommended && (
                                  <Badge variant="secondary" className="text-xs">
                                    Recommended
                                  </Badge>
                                )}
                              </h4>
                              <p className="text-sm text-muted-foreground">{config.description}</p>
                              <div className="flex items-center space-x-2 mt-1">
                                <Badge variant="outline" className="text-xs">
                                  ${config.pricing.inputTokens * 1000000}/1M tokens
                                </Badge>
                                <Badge variant="outline" className="text-xs">
                                  {config.models.length} models
                                </Badge>
                              </div>
                            </div>
                          </div>
                          {selectedProviders.includes(key) && <CheckCircle className="h-5 w-5 text-blue-600" />}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>

                {selectedProviders.length === 0 && (
                  <Alert>
                    <AlertDescription>Please select at least one AI provider to continue.</AlertDescription>
                  </Alert>
                )}
              </div>
            )}

            {/* Step 2: API Key Configuration */}
            {currentStep === 2 && (
              <div className="space-y-6">
                {selectedProviders.map((providerKey) => {
                  const config = SUPPORTED_PROVIDERS[providerKey as keyof typeof SUPPORTED_PROVIDERS]
                  const isValid = testResults[providerKey]
                  const isTesting = testing[providerKey]

                  return (
                    <Card key={providerKey}>
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="text-xl">{config.icon}</div>
                            <div>
                              <CardTitle className="text-lg">{config.name}</CardTitle>
                              <CardDescription>{config.setup.description}</CardDescription>
                            </div>
                          </div>
                          <Button variant="outline" size="sm" onClick={() => openHelp(providerKey)}>
                            <ExternalLink className="h-4 w-4 mr-2" />
                            Help
                          </Button>
                        </div>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="space-y-2">
                          <Label htmlFor={`api-key-${providerKey}`}>API Key</Label>
                          <div className="flex space-x-2">
                            <div className="relative flex-1">
                              <Input
                                id={`api-key-${providerKey}`}
                                type={showApiKey[providerKey] ? "text" : "password"}
                                value={apiKeys[providerKey] || ""}
                                onChange={(e) =>
                                  setApiKeys((prev) => ({
                                    ...prev,
                                    [providerKey]: e.target.value,
                                  }))
                                }
                                placeholder={`Enter your ${config.name} API key`}
                                className={isValid ? "border-green-500" : ""}
                              />
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                className="absolute right-2 top-1/2 -translate-y-1/2"
                                onClick={() =>
                                  setShowApiKey((prev) => ({
                                    ...prev,
                                    [providerKey]: !prev[providerKey],
                                  }))
                                }
                              >
                                {showApiKey[providerKey] ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                              </Button>
                            </div>
                            <Button
                              onClick={() => testApiKey(providerKey, apiKeys[providerKey])}
                              disabled={!apiKeys[providerKey] || isTesting}
                              variant={isValid ? "default" : "outline"}
                            >
                              {isTesting ? (
                                <div className="h-4 w-4 animate-spin rounded-full border-2 border-background border-t-transparent" />
                              ) : isValid ? (
                                <CheckCircle className="h-4 w-4" />
                              ) : (
                                "Test"
                              )}
                            </Button>
                          </div>
                          {isValid && (
                            <p className="text-sm text-green-600 flex items-center space-x-1">
                              <CheckCircle className="h-4 w-4" />
                              <span>Connection verified!</span>
                            </p>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  )
                })}
              </div>
            )}

            {/* Step 3: Complete */}
            {currentStep === 3 && (
              <div className="text-center space-y-6">
                <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                  <CheckCircle className="h-8 w-8 text-green-600" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold mb-2">You're All Set! 🎉</h3>
                  <p className="text-muted-foreground">
                    Your AI providers are configured and ready to create amazing content.
                  </p>
                </div>

                <div className="grid md:grid-cols-2 gap-4 max-w-2xl mx-auto">
                  {selectedProviders.map((providerKey) => {
                    const config = SUPPORTED_PROVIDERS[providerKey as keyof typeof SUPPORTED_PROVIDERS]
                    const isValid = testResults[providerKey]

                    return (
                      <div key={providerKey} className="flex items-center space-x-3 p-3 rounded-lg bg-gray-50">
                        <div className="text-lg">{config.icon}</div>
                        <div className="flex-1 text-left">
                          <p className="font-medium">{config.name}</p>
                          <p className="text-sm text-muted-foreground">{config.models.length} models available</p>
                        </div>
                        {isValid ? (
                          <CheckCircle className="h-5 w-5 text-green-600" />
                        ) : (
                          <div className="h-5 w-5 rounded-full bg-yellow-200" />
                        )}
                      </div>
                    )
                  })}
                </div>

                <Alert>
                  <Sparkles className="h-4 w-4" />
                  <AlertDescription>
                    <strong>What's Next?</strong> Head to the AI Studio to start generating content, or create your
                    first campaign!
                  </AlertDescription>
                </Alert>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Navigation */}
        <div className="flex items-center justify-between">
          <Button
            variant="outline"
            onClick={() => (currentStep > 0 ? setCurrentStep((prev) => prev - 1) : router.back())}
            disabled={loading}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            {currentStep === 0 ? "Cancel" : "Back"}
          </Button>

          <div className="flex space-x-2">
            {currentStep < steps.length - 1 ? (
              <Button
                onClick={() => setCurrentStep((prev) => prev + 1)}
                disabled={
                  (currentStep === 1 && selectedProviders.length === 0) ||
                  (currentStep === 2 && selectedProviders.some((p) => !testResults[p]))
                }
              >
                Continue
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            ) : (
              <Button
                onClick={completeSetup}
                disabled={loading || selectedProviders.some((p) => !testResults[p])}
                className="bg-green-600 hover:bg-green-700"
              >
                {loading ? (
                  <>
                    <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-background border-t-transparent" />
                    Setting up...
                  </>
                ) : (
                  <>
                    Complete Setup
                    <CheckCircle className="h-4 w-4 ml-2" />
                  </>
                )}
              </Button>
            )}
          </div>
        </div>

        {/* Help Dialog */}
        <Dialog open={helpDialogOpen} onOpenChange={setHelpDialogOpen}>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>
                {selectedProviderHelp &&
                  SUPPORTED_PROVIDERS[selectedProviderHelp as keyof typeof SUPPORTED_PROVIDERS]?.setup.title}
              </DialogTitle>
              <DialogDescription>Follow these steps to get your API key:</DialogDescription>
            </DialogHeader>
            {selectedProviderHelp && (
              <div className="space-y-4">
                <ol className="space-y-2">
                  {SUPPORTED_PROVIDERS[selectedProviderHelp as keyof typeof SUPPORTED_PROVIDERS]?.setup.steps.map(
                    (step, index) => (
                      <li key={index} className="flex items-start space-x-2">
                        <span className="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium">
                          {index + 1}
                        </span>
                        <span className="text-sm">{step}</span>
                      </li>
                    ),
                  )}
                </ol>
                <Button
                  variant="outline"
                  className="w-full bg-transparent"
                  onClick={() =>
                    window.open(
                      SUPPORTED_PROVIDERS[selectedProviderHelp as keyof typeof SUPPORTED_PROVIDERS]?.setup.helpUrl,
                      "_blank",
                    )
                  }
                >
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Open {SUPPORTED_PROVIDERS[selectedProviderHelp as keyof typeof SUPPORTED_PROVIDERS]?.name} Dashboard
                </Button>
              </div>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </div>
  )
}

import { Suspense } from "react"
import { Overview } from "@/components/dashboard/overview"
import { Engagement<PERSON>hart } from "@/components/dashboard/engagement-chart"
import { TopPosts } from "@/components/dashboard/top-posts"
import { RecentActivity } from "@/components/dashboard/recent-activity"
import { CampaignPerformance } from "@/components/dashboard/campaign-performance"
import { PlatformMetrics } from "@/components/dashboard/platform-metrics"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"

function DashboardSkeleton() {
  return (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-4" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-16 mb-2" />
              <Skeleton className="h-3 w-32" />
            </CardContent>
          </Card>
        ))}
      </div>
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <Card className="col-span-4">
          <CardHeader>
            <Skeleton className="h-6 w-32" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-80 w-full" />
          </CardContent>
        </Card>
        <Card className="col-span-3">
          <CardHeader>
            <Skeleton className="h-6 w-24" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Array.from({ length: 5 }).map((_, i) => (
                <div key={i} className="flex items-center space-x-4">
                  <Skeleton className="h-12 w-12 rounded" />
                  <div className="space-y-2 flex-1">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-3 w-2/3" />
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default function DashboardPage() {
  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Dashboard</h1>
      </div>

      <Suspense fallback={<DashboardSkeleton />}>
        <div className="space-y-6">
          {/* Overview Cards */}
          <Overview />

          {/* Charts and Analytics */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
            <div className="col-span-4">
              <EngagementChart />
            </div>
            <div className="col-span-3">
              <TopPosts />
            </div>
          </div>

          {/* Performance Tables */}
          <div className="grid gap-4 md:grid-cols-2">
            <CampaignPerformance />
            <RecentActivity />
          </div>

          {/* Platform Metrics */}
          <PlatformMetrics />
        </div>
      </Suspense>
    </div>
  )
}

import type { SocialAccount } from "./types"
import { decrypt, encrypt } from "./encryption"
import { prisma } from "./prisma"
import { jobQueue } from "./job-queue"

export interface SocialMediaPost {
  content: string
  mediaUrls?: string[]
  scheduledTime?: Date
  hashtags?: string[]
}

export interface PostResult {
  success: boolean
  postId?: string
  error?: string
  metrics?: {
    reach?: number
    impressions?: number
    engagement?: number
    likes?: number
    comments?: number
    shares?: number
  }
}

export abstract class SocialMediaProvider {
  protected account: SocialAccount
  protected accessToken: string

  constructor(account: SocialAccount) {
    this.account = account
    this.accessToken = decrypt(account.accessTokenEncrypted)
  }

  abstract publishPost(post: SocialMediaPost): Promise<PostResult>
  abstract schedulePost(post: SocialMediaPost): Promise<PostResult>
  abstract getAnalytics(postId: string): Promise<any>
  abstract refreshToken(): Promise<boolean>
  abstract getFollowerCount(): Promise<number>
}

export class Facebook<PERSON>rovider extends SocialMediaProvider {
  async publishPost(post: SocialMediaPost): Promise<PostResult> {
    try {
      // Upload media if present
      let mediaIds: string[] = []
      if (post.mediaUrls && post.mediaUrls.length > 0) {
        mediaIds = await this.uploadMedia(post.mediaUrls)
      }

      const postData: any = {
        message: post.content,
      }

      // Add hashtags to content
      if (post.hashtags && post.hashtags.length > 0) {
        postData.message += "\n\n" + post.hashtags.map((tag) => `#${tag}`).join(" ")
      }

      // Add media attachments
      if (mediaIds.length > 0) {
        if (mediaIds.length === 1) {
          postData.object_attachment = mediaIds[0]
        } else {
          postData.attached_media = mediaIds.map((id) => ({ media_fbid: id }))
        }
      }

      const response = await fetch(`https://graph.facebook.com/v18.0/${this.account.accountId}/feed`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${this.accessToken}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(postData),
      })

      const data = await response.json()

      if (response.ok) {
        // Get initial metrics
        const metrics = await this.getPostMetrics(data.id)

        return {
          success: true,
          postId: data.id,
          metrics,
        }
      } else {
        // Handle token expiration
        if (data.error?.code === 190) {
          await this.scheduleTokenRefresh()
        }

        return { success: false, error: data.error?.message || "Unknown error" }
      }
    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : "Unknown error" }
    }
  }

  async schedulePost(post: SocialMediaPost): Promise<PostResult> {
    if (!post.scheduledTime) {
      return { success: false, error: "Scheduled time is required" }
    }

    try {
      const scheduledTime = Math.floor(post.scheduledTime.getTime() / 1000)

      // Upload media first if present
      let mediaIds: string[] = []
      if (post.mediaUrls && post.mediaUrls.length > 0) {
        mediaIds = await this.uploadMedia(post.mediaUrls)
      }

      const postData: any = {
        message: post.content,
        scheduled_publish_time: scheduledTime,
        published: false,
      }

      // Add hashtags
      if (post.hashtags && post.hashtags.length > 0) {
        postData.message += "\n\n" + post.hashtags.map((tag) => `#${tag}`).join(" ")
      }

      if (mediaIds.length > 0) {
        if (mediaIds.length === 1) {
          postData.object_attachment = mediaIds[0]
        } else {
          postData.attached_media = mediaIds.map((id) => ({ media_fbid: id }))
        }
      }

      const response = await fetch(`https://graph.facebook.com/v18.0/${this.account.accountId}/feed`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${this.accessToken}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(postData),
      })

      const data = await response.json()

      if (response.ok) {
        return { success: true, postId: data.id }
      } else {
        return { success: false, error: data.error?.message || "Unknown error" }
      }
    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : "Unknown error" }
    }
  }

  async getAnalytics(postId: string): Promise<any> {
    try {
      const response = await fetch(
        `https://graph.facebook.com/v18.0/${postId}/insights?metric=post_impressions,post_clicks,post_reactions_like_total,post_reactions_love_total,post_reactions_wow_total,post_reactions_haha_total,post_reactions_sorry_total,post_reactions_anger_total,post_video_views,post_engaged_users&access_token=${this.accessToken}`,
      )

      const data = await response.json()

      if (response.ok) {
        const metrics = {
          impressions: 0,
          clicks: 0,
          likes: 0,
          loves: 0,
          engagement: 0,
          videoViews: 0,
        }

        data.data?.forEach((insight: any) => {
          switch (insight.name) {
            case "post_impressions":
              metrics.impressions = insight.values[0]?.value || 0
              break
            case "post_clicks":
              metrics.clicks = insight.values[0]?.value || 0
              break
            case "post_reactions_like_total":
              metrics.likes = insight.values[0]?.value || 0
              break
            case "post_reactions_love_total":
              metrics.loves = insight.values[0]?.value || 0
              break
            case "post_engaged_users":
              metrics.engagement = insight.values[0]?.value || 0
              break
            case "post_video_views":
              metrics.videoViews = insight.values[0]?.value || 0
              break
          }
        })

        return metrics
      }

      return null
    } catch (error) {
      console.error("Failed to fetch Facebook analytics:", error)
      return null
    }
  }

  async refreshToken(): Promise<boolean> {
    try {
      const response = await fetch(
        `https://graph.facebook.com/v18.0/oauth/access_token?grant_type=fb_exchange_token&client_id=${process.env.FACEBOOK_CLIENT_ID}&client_secret=${process.env.FACEBOOK_CLIENT_SECRET}&fb_exchange_token=${this.accessToken}`,
      )

      const data = await response.json()

      if (response.ok && data.access_token) {
        await prisma.socialAccount.update({
          where: { id: this.account.id },
          data: {
            accessTokenEncrypted: encrypt(data.access_token),
            expiresAt: data.expires_in ? new Date(Date.now() + data.expires_in * 1000) : null,
            healthStatus: "healthy",
            lastHealthCheck: new Date(),
          },
        })

        this.accessToken = data.access_token
        return true
      }

      return false
    } catch (error) {
      console.error("Failed to refresh Facebook token:", error)
      return false
    }
  }

  async getFollowerCount(): Promise<number> {
    try {
      const response = await fetch(
        `https://graph.facebook.com/v18.0/${this.account.accountId}?fields=followers_count&access_token=${this.accessToken}`,
      )

      const data = await response.json()

      if (response.ok) {
        return data.followers_count || 0
      }

      return 0
    } catch (error) {
      console.error("Failed to get Facebook follower count:", error)
      return 0
    }
  }

  private async uploadMedia(mediaUrls: string[]): Promise<string[]> {
    const mediaIds: string[] = []

    for (const url of mediaUrls) {
      try {
        const response = await fetch(`https://graph.facebook.com/v18.0/${this.account.accountId}/photos`, {
          method: "POST",
          headers: {
            Authorization: `Bearer ${this.accessToken}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            url: url,
            published: false,
          }),
        })

        const data = await response.json()
        if (response.ok && data.id) {
          mediaIds.push(data.id)
        }
      } catch (error) {
        console.error("Failed to upload media:", error)
      }
    }

    return mediaIds
  }

  private async getPostMetrics(postId: string): Promise<any> {
    try {
      const response = await fetch(
        `https://graph.facebook.com/v18.0/${postId}?fields=likes.summary(true),comments.summary(true),shares&access_token=${this.accessToken}`,
      )

      const data = await response.json()

      if (response.ok) {
        return {
          likes: data.likes?.summary?.total_count || 0,
          comments: data.comments?.summary?.total_count || 0,
          shares: data.shares?.count || 0,
        }
      }

      return {}
    } catch (error) {
      console.error("Failed to get post metrics:", error)
      return {}
    }
  }

  private async scheduleTokenRefresh() {
    await jobQueue.addJob(
      this.account.tenantId,
      "refresh_social_token",
      { accountId: this.account.id },
      new Date(Date.now() + 5 * 60 * 1000), // Retry in 5 minutes
    )
  }
}

export class TwitterProvider extends SocialMediaProvider {
  async publishPost(post: SocialMediaPost): Promise<PostResult> {
    try {
      // Upload media first if present
      let mediaIds: string[] = []
      if (post.mediaUrls && post.mediaUrls.length > 0) {
        mediaIds = await this.uploadMedia(post.mediaUrls)
      }

      let content = post.content

      // Add hashtags to content
      if (post.hashtags && post.hashtags.length > 0) {
        content += " " + post.hashtags.map((tag) => `#${tag}`).join(" ")
      }

      // Ensure content is within Twitter's character limit
      if (content.length > 280) {
        content = content.substring(0, 277) + "..."
      }

      const tweetData: any = {
        text: content,
      }

      if (mediaIds.length > 0) {
        tweetData.media = { media_ids: mediaIds }
      }

      const response = await fetch("https://api.twitter.com/2/tweets", {
        method: "POST",
        headers: {
          Authorization: `Bearer ${this.accessToken}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(tweetData),
      })

      const data = await response.json()

      if (response.ok) {
        const metrics = await this.getTweetMetrics(data.data.id)

        return {
          success: true,
          postId: data.data.id,
          metrics,
        }
      } else {
        return { success: false, error: data.errors?.[0]?.message || "Unknown error" }
      }
    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : "Unknown error" }
    }
  }

  async schedulePost(post: SocialMediaPost): Promise<PostResult> {
    // Twitter API v2 doesn't support native scheduling
    // Use job queue for scheduling
    if (!post.scheduledTime) {
      return { success: false, error: "Scheduled time is required" }
    }

    // Schedule the post via job queue
    await jobQueue.addJob(
      this.account.tenantId,
      "publish_post",
      {
        accountId: this.account.id,
        post: {
          content: post.content,
          mediaUrls: post.mediaUrls,
          hashtags: post.hashtags,
        },
      },
      post.scheduledTime,
    )

    return { success: true, postId: `scheduled_${Date.now()}` }
  }

  async getAnalytics(postId: string): Promise<any> {
    try {
      const response = await fetch(
        `https://api.twitter.com/2/tweets/${postId}?tweet.fields=public_metrics,non_public_metrics&expansions=author_id`,
        {
          headers: {
            Authorization: `Bearer ${this.accessToken}`,
          },
        },
      )

      const data = await response.json()

      if (response.ok) {
        const metrics = data.data?.public_metrics || {}
        return {
          retweets: metrics.retweet_count || 0,
          likes: metrics.like_count || 0,
          replies: metrics.reply_count || 0,
          quotes: metrics.quote_count || 0,
          impressions: data.data?.non_public_metrics?.impression_count || 0,
        }
      }

      return null
    } catch (error) {
      console.error("Failed to fetch Twitter analytics:", error)
      return null
    }
  }

  async refreshToken(): Promise<boolean> {
    try {
      const response = await fetch("https://api.twitter.com/2/oauth2/token", {
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          Authorization: `Basic ${Buffer.from(`${process.env.TWITTER_CLIENT_ID}:${process.env.TWITTER_CLIENT_SECRET}`).toString("base64")}`,
        },
        body: new URLSearchParams({
          grant_type: "refresh_token",
          refresh_token: decrypt(this.account.refreshTokenEncrypted || ""),
        }),
      })

      const data = await response.json()

      if (response.ok && data.access_token) {
        await prisma.socialAccount.update({
          where: { id: this.account.id },
          data: {
            accessTokenEncrypted: encrypt(data.access_token),
            refreshTokenEncrypted: data.refresh_token ? encrypt(data.refresh_token) : undefined,
            expiresAt: data.expires_in ? new Date(Date.now() + data.expires_in * 1000) : null,
            healthStatus: "healthy",
            lastHealthCheck: new Date(),
          },
        })

        this.accessToken = data.access_token
        return true
      }

      return false
    } catch (error) {
      console.error("Failed to refresh Twitter token:", error)
      return false
    }
  }

  async getFollowerCount(): Promise<number> {
    try {
      const response = await fetch(`https://api.twitter.com/2/users/me?user.fields=public_metrics`, {
        headers: {
          Authorization: `Bearer ${this.accessToken}`,
        },
      })

      const data = await response.json()

      if (response.ok) {
        return data.data?.public_metrics?.followers_count || 0
      }

      return 0
    } catch (error) {
      console.error("Failed to get Twitter follower count:", error)
      return 0
    }
  }

  private async uploadMedia(mediaUrls: string[]): Promise<string[]> {
    const mediaIds: string[] = []

    for (const url of mediaUrls) {
      try {
        // First, fetch the media
        const mediaResponse = await fetch(url)
        const mediaBuffer = await mediaResponse.arrayBuffer()

        // Upload to Twitter
        const formData = new FormData()
        formData.append("media", new Blob([mediaBuffer]))

        const response = await fetch("https://upload.twitter.com/1.1/media/upload.json", {
          method: "POST",
          headers: {
            Authorization: `Bearer ${this.accessToken}`,
          },
          body: formData,
        })

        const data = await response.json()
        if (response.ok && data.media_id_string) {
          mediaIds.push(data.media_id_string)
        }
      } catch (error) {
        console.error("Failed to upload media to Twitter:", error)
      }
    }

    return mediaIds
  }

  private async getTweetMetrics(tweetId: string): Promise<any> {
    try {
      const response = await fetch(`https://api.twitter.com/2/tweets/${tweetId}?tweet.fields=public_metrics`, {
        headers: {
          Authorization: `Bearer ${this.accessToken}`,
        },
      })

      const data = await response.json()

      if (response.ok) {
        const metrics = data.data?.public_metrics || {}
        return {
          retweets: metrics.retweet_count || 0,
          likes: metrics.like_count || 0,
          replies: metrics.reply_count || 0,
          quotes: metrics.quote_count || 0,
        }
      }

      return {}
    } catch (error) {
      console.error("Failed to get tweet metrics:", error)
      return {}
    }
  }
}

export class SocialMediaManager {
  private providers: Map<string, SocialMediaProvider> = new Map()

  addProvider(account: SocialAccount) {
    let provider: SocialMediaProvider

    switch (account.platform) {
      case "facebook":
        provider = new FacebookProvider(account)
        break
      case "twitter":
        provider = new TwitterProvider(account)
        break
      case "linkedin":
        provider = new LinkedInProvider(account)
        break
      default:
        throw new Error(`Unsupported platform: ${account.platform}`)
    }

    this.providers.set(account.id, provider)
  }

  getProvider(accountId: string): SocialMediaProvider | undefined {
    return this.providers.get(accountId)
  }

  async publishToMultiplePlatforms(accountIds: string[], post: SocialMediaPost): Promise<Record<string, PostResult>> {
    const results: Record<string, PostResult> = {}

    await Promise.all(
      accountIds.map(async (accountId) => {
        const provider = this.providers.get(accountId)
        if (provider) {
          try {
            results[accountId] = await provider.publishPost(post)

            // Store analytics data if successful
            if (results[accountId].success && results[accountId].metrics) {
              await this.storeAnalytics(accountId, results[accountId].postId!, results[accountId].metrics!)
            }
          } catch (error) {
            results[accountId] = {
              success: false,
              error: error instanceof Error ? error.message : "Unknown error",
            }
          }
        } else {
          results[accountId] = { success: false, error: "Provider not found" }
        }
      }),
    )

    return results
  }

  private async storeAnalytics(accountId: string, postId: string, metrics: any) {
    try {
      const account = await prisma.socialAccount.findUnique({
        where: { id: accountId },
      })

      if (account) {
        // Store initial metrics
        for (const [metricType, value] of Object.entries(metrics)) {
          if (typeof value === "number") {
            await prisma.analyticsEvent.create({
              data: {
                tenantId: account.tenantId,
                eventType: metricType,
                platform: account.platform,
                value: value as number,
                metadata: { postId, accountId },
              },
            })
          }
        }
      }
    } catch (error) {
      console.error("Failed to store analytics:", error)
    }
  }
}

// LinkedIn Provider implementation
export class LinkedInProvider extends SocialMediaProvider {
  async publishPost(post: SocialMediaPost): Promise<PostResult> {
    try {
      // Get user profile ID
      const profileResponse = await fetch("https://api.linkedin.com/v2/people/~", {
        headers: {
          Authorization: `Bearer ${this.accessToken}`,
        },
      })

      const profileData = await profileResponse.json()
      const authorUrn = `urn:li:person:${profileData.id}`

      // Upload media if present
      let mediaAssets: any[] = []
      if (post.mediaUrls && post.mediaUrls.length > 0) {
        mediaAssets = await this.uploadMedia(post.mediaUrls, authorUrn)
      }

      let content = post.content

      // Add hashtags to content
      if (post.hashtags && post.hashtags.length > 0) {
        content += "\n\n" + post.hashtags.map((tag) => `#${tag}`).join(" ")
      }

      const shareData: any = {
        author: authorUrn,
        lifecycleState: "PUBLISHED",
        specificContent: {
          "com.linkedin.ugc.ShareContent": {
            shareCommentary: {
              text: content,
            },
            shareMediaCategory: mediaAssets.length > 0 ? "IMAGE" : "NONE",
          },
        },
        visibility: {
          "com.linkedin.ugc.MemberNetworkVisibility": "PUBLIC",
        },
      }

      if (mediaAssets.length > 0) {
        shareData.specificContent["com.linkedin.ugc.ShareContent"].media = mediaAssets
      }

      const response = await fetch("https://api.linkedin.com/v2/ugcPosts", {
        method: "POST",
        headers: {
          Authorization: `Bearer ${this.accessToken}`,
          "Content-Type": "application/json",
          "X-Restli-Protocol-Version": "2.0.0",
        },
        body: JSON.stringify(shareData),
      })

      const data = await response.json()

      if (response.ok) {
        return { success: true, postId: data.id }
      } else {
        return { success: false, error: data.message || "Unknown error" }
      }
    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : "Unknown error" }
    }
  }

  async schedulePost(post: SocialMediaPost): Promise<PostResult> {
    // LinkedIn doesn't support native scheduling through API
    // Use job queue for scheduling
    if (!post.scheduledTime) {
      return { success: false, error: "Scheduled time is required" }
    }

    await jobQueue.addJob(
      this.account.tenantId,
      "publish_post",
      {
        accountId: this.account.id,
        post: {
          content: post.content,
          mediaUrls: post.mediaUrls,
          hashtags: post.hashtags,
        },
      },
      post.scheduledTime,
    )

    return { success: true, postId: `scheduled_${Date.now()}` }
  }

  async getAnalytics(postId: string): Promise<any> {
    try {
      const response = await fetch(`https://api.linkedin.com/v2/socialActions/${postId}/statistics`, {
        headers: {
          Authorization: `Bearer ${this.accessToken}`,
        },
      })

      const data = await response.json()

      if (response.ok) {
        return {
          likes: data.numLikes || 0,
          comments: data.numComments || 0,
          shares: data.numShares || 0,
          clicks: data.numClicks || 0,
          impressions: data.numImpressions || 0,
        }
      }

      return null
    } catch (error) {
      console.error("Failed to fetch LinkedIn analytics:", error)
      return null
    }
  }

  async refreshToken(): Promise<boolean> {
    try {
      const response = await fetch("https://www.linkedin.com/oauth/v2/accessToken", {
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        body: new URLSearchParams({
          grant_type: "refresh_token",
          refresh_token: decrypt(this.account.refreshTokenEncrypted || ""),
          client_id: process.env.LINKEDIN_CLIENT_ID || "",
          client_secret: process.env.LINKEDIN_CLIENT_SECRET || "",
        }),
      })

      const data = await response.json()

      if (response.ok && data.access_token) {
        await prisma.socialAccount.update({
          where: { id: this.account.id },
          data: {
            accessTokenEncrypted: encrypt(data.access_token),
            refreshTokenEncrypted: data.refresh_token ? encrypt(data.refresh_token) : undefined,
            expiresAt: data.expires_in ? new Date(Date.now() + data.expires_in * 1000) : null,
            healthStatus: "healthy",
            lastHealthCheck: new Date(),
          },
        })

        this.accessToken = data.access_token
        return true
      }

      return false
    } catch (error) {
      console.error("Failed to refresh LinkedIn token:", error)
      return false
    }
  }

  async getFollowerCount(): Promise<number> {
    try {
      const response = await fetch("https://api.linkedin.com/v2/people/~:(num-connections)", {
        headers: {
          Authorization: `Bearer ${this.accessToken}`,
        },
      })

      const data = await response.json()

      if (response.ok) {
        return data.numConnections || 0
      }

      return 0
    } catch (error) {
      console.error("Failed to get LinkedIn follower count:", error)
      return 0
    }
  }

  private async uploadMedia(mediaUrls: string[], authorUrn: string): Promise<any[]> {
    const mediaAssets: any[] = []

    for (const url of mediaUrls) {
      try {
        // Register upload
        const registerResponse = await fetch("https://api.linkedin.com/v2/assets?action=registerUpload", {
          method: "POST",
          headers: {
            Authorization: `Bearer ${this.accessToken}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            registerUploadRequest: {
              recipes: ["urn:li:digitalmediaRecipe:feedshare-image"],
              owner: authorUrn,
              serviceRelationships: [
                {
                  relationshipType: "OWNER",
                  identifier: "urn:li:userGeneratedContent",
                },
              ],
            },
          }),
        })

        const registerData = await registerResponse.json()

        if (registerResponse.ok) {
          // Upload the actual media
          const mediaResponse = await fetch(url)
          const mediaBuffer = await mediaResponse.arrayBuffer()

          const uploadResponse = await fetch(
            registerData.value.uploadMechanism["com.linkedin.digitalmedia.uploading.MediaUploadHttpRequest"].uploadUrl,
            {
              method: "POST",
              headers: {
                Authorization: `Bearer ${this.accessToken}`,
              },
              body: mediaBuffer,
            },
          )

          if (uploadResponse.ok) {
            mediaAssets.push({
              status: "READY",
              description: {
                text: "Uploaded media",
              },
              media: registerData.value.asset,
              title: {
                text: "Media",
              },
            })
          }
        }
      } catch (error) {
        console.error("Failed to upload media to LinkedIn:", error)
      }
    }

    return mediaAssets
  }
}

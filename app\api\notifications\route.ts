import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const notifications = await prisma.notification.findMany({
      where: {
        tenantId: session.user.tenantId,
        userId: session.user.id,
      },
      orderBy: { createdAt: "desc" },
      take: 50,
    })

    return NextResponse.json({ notifications })
  } catch (error) {
    console.error("Failed to fetch notifications:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { type, title, message, data } = await request.json()

    const notification = await prisma.notification.create({
      data: {
        tenantId: session.user.tenantId,
        userId: session.user.id,
        type,
        title,
        message,
        data: data || {},
      },
    })

    return NextResponse.json({ notification }, { status: 201 })
  } catch (error) {
    console.error("Failed to create notification:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

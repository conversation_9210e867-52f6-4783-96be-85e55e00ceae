"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import {
  ArrowLeft,
  ArrowRight,
  CheckCircle,
  ExternalLink,
  Share2,
  Facebook,
  Twitter,
  Linkedin,
  Instagram,
  Youtube,
  Shield,
  Zap,
  Users,
} from "lucide-react"
import { useToast } from "@/hooks/use-toast"

const SOCIAL_PLATFORMS = {
  facebook: {
    name: "Facebook",
    icon: Facebook,
    color: "bg-blue-600",
    description: "Connect your Facebook pages and profiles",
    features: ["Post to pages", "Schedule content", "View analytics", "Manage comments"],
    audience: "2.9B+ users worldwide",
    bestFor: "Brand awareness, community building",
    setup: {
      title: "Connect Facebook",
      steps: [
        "Click 'Connect Facebook' below",
        "Log in to your Facebook account",
        "Select the pages you want to manage",
        "Grant necessary permissions",
        "You're ready to post!",
      ],
    },
  },
  instagram: {
    name: "Instagram",
    icon: Instagram,
    color: "bg-gradient-to-r from-purple-500 to-pink-500",
    description: "Manage your Instagram business accounts",
    features: ["Post photos & videos", "Stories", "Reels", "Shopping tags"],
    audience: "2B+ users worldwide",
    bestFor: "Visual storytelling, younger demographics",
    setup: {
      title: "Connect Instagram",
      steps: [
        "Ensure you have an Instagram Business account",
        "Connect your Facebook page first",
        "Link Instagram to your Facebook page",
        "Grant posting permissions",
        "Start sharing visual content!",
      ],
    },
  },
  twitter: {
    name: "Twitter/X",
    icon: Twitter,
    color: "bg-black",
    description: "Post and engage on Twitter/X",
    features: ["Tweet & retweet", "Threads", "Spaces", "Real-time engagement"],
    audience: "450M+ users worldwide",
    bestFor: "News, real-time updates, thought leadership",
    setup: {
      title: "Connect Twitter/X",
      steps: [
        "Click 'Connect Twitter' below",
        "Authorize our app in Twitter",
        "Grant read and write permissions",
        "Confirm your account selection",
        "Ready to tweet!",
      ],
    },
  },
  linkedin: {
    name: "LinkedIn",
    icon: Linkedin,
    color: "bg-blue-700",
    description: "Share professional content on LinkedIn",
    features: ["Company pages", "Personal posts", "Articles", "Professional network"],
    audience: "900M+ professionals",
    bestFor: "B2B marketing, professional networking",
    setup: {
      title: "Connect LinkedIn",
      steps: [
        "Click 'Connect LinkedIn' below",
        "Sign in to your LinkedIn account",
        "Select personal or company page",
        "Authorize content posting",
        "Start professional networking!",
      ],
    },
  },
  youtube: {
    name: "YouTube",
    icon: Youtube,
    color: "bg-red-600",
    description: "Upload and manage YouTube content",
    features: ["Video uploads", "Shorts", "Community posts", "Analytics"],
    audience: "2.7B+ users worldwide",
    bestFor: "Video content, tutorials, entertainment",
    setup: {
      title: "Connect YouTube",
      steps: [
        "Ensure you have a YouTube channel",
        "Click 'Connect YouTube' below",
        "Sign in with Google account",
        "Grant channel management permissions",
        "Start uploading videos!",
      ],
    },
  },
  tiktok: {
    name: "TikTok",
    icon: () => <div className="w-5 h-5 bg-black rounded" />,
    color: "bg-black",
    description: "Create and share TikTok videos",
    features: ["Short videos", "Trending sounds", "Effects", "Viral content"],
    audience: "1B+ users worldwide",
    bestFor: "Gen Z engagement, viral marketing",
    setup: {
      title: "Connect TikTok",
      steps: [
        "Ensure you have a TikTok Business account",
        "Click 'Connect TikTok' below",
        "Authorize our application",
        "Grant content posting permissions",
        "Start creating viral content!",
      ],
    },
  },
}

export default function SocialConnectWizard() {
  const router = useRouter()
  const { toast } = useToast()

  const [currentStep, setCurrentStep] = useState(0)
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>([])
  const [connecting, setConnecting] = useState<string | null>(null)
  const [connected, setConnected] = useState<string[]>([])
  const [helpDialogOpen, setHelpDialogOpen] = useState(false)
  const [selectedPlatformHelp, setSelectedPlatformHelp] = useState<string>("")

  const steps = [
    {
      title: "Welcome to Social Connect",
      description: "Connect your social media accounts in 3 simple steps",
    },
    {
      title: "Choose Platforms",
      description: "Select which social media platforms you want to connect",
    },
    {
      title: "Connect Accounts",
      description: "Securely connect your selected social media accounts",
    },
    {
      title: "You're Connected!",
      description: "Your social accounts are ready for content publishing",
    },
  ]

  async function connectPlatform(platformKey: string) {
    setConnecting(platformKey)

    try {
      // Simulate OAuth flow - in real implementation, this would redirect to OAuth
      await new Promise((resolve) => setTimeout(resolve, 2000))

      // Mock successful connection
      setConnected((prev) => [...prev, platformKey])
      toast({
        title: "Connected Successfully! ✅",
        description: `${SOCIAL_PLATFORMS[platformKey as keyof typeof SOCIAL_PLATFORMS].name} is now connected`,
      })
    } catch (error) {
      toast({
        title: "Connection Failed",
        description: "Please try again or check your account permissions",
        variant: "destructive",
      })
    } finally {
      setConnecting(null)
    }
  }

  function openHelp(platformKey: string) {
    setSelectedPlatformHelp(platformKey)
    setHelpDialogOpen(true)
  }

  const progress = ((currentStep + 1) / steps.length) * 100

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-100 p-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <Share2 className="h-8 w-8 text-green-600" />
            <h1 className="text-3xl font-bold">Social Media Setup</h1>
          </div>
          <Progress value={progress} className="w-full max-w-md mx-auto" />
          <p className="text-muted-foreground mt-2">
            Step {currentStep + 1} of {steps.length}: {steps[currentStep].title}
          </p>
        </div>

        {/* Step Content */}
        <Card className="mb-8">
          <CardHeader className="text-center">
            <CardTitle className="flex items-center justify-center space-x-2">
              {currentStep === 0 && <Share2 className="h-6 w-6 text-green-600" />}
              {currentStep === 1 && <Users className="h-6 w-6 text-blue-600" />}
              {currentStep === 2 && <Shield className="h-6 w-6 text-purple-600" />}
              {currentStep === 3 && <CheckCircle className="h-6 w-6 text-emerald-600" />}
              <span>{steps[currentStep].title}</span>
            </CardTitle>
            <CardDescription>{steps[currentStep].description}</CardDescription>
          </CardHeader>
          <CardContent>
            {/* Step 0: Welcome */}
            {currentStep === 0 && (
              <div className="text-center space-y-6">
                <div className="grid md:grid-cols-3 gap-4">
                  <div className="p-4 rounded-lg bg-green-50">
                    <Share2 className="h-8 w-8 text-green-600 mx-auto mb-2" />
                    <h3 className="font-semibold">Multi-Platform</h3>
                    <p className="text-sm text-muted-foreground">Post to all platforms at once</p>
                  </div>
                  <div className="p-4 rounded-lg bg-blue-50">
                    <Shield className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                    <h3 className="font-semibold">Secure Connection</h3>
                    <p className="text-sm text-muted-foreground">Bank-level security with OAuth</p>
                  </div>
                  <div className="p-4 rounded-lg bg-purple-50">
                    <Zap className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                    <h3 className="font-semibold">Instant Publishing</h3>
                    <p className="text-sm text-muted-foreground">Schedule and auto-post content</p>
                  </div>
                </div>
                <Alert>
                  <Shield className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Your Privacy Matters:</strong> We only request the minimum permissions needed to post
                    content. You can revoke access anytime.
                  </AlertDescription>
                </Alert>
              </div>
            )}

            {/* Step 1: Platform Selection */}
            {currentStep === 1 && (
              <div className="space-y-6">
                <div className="text-center mb-6">
                  <h3 className="text-lg font-semibold mb-2">Choose Your Platforms</h3>
                  <p className="text-muted-foreground">
                    Select the social media platforms where you want to publish content
                  </p>
                </div>

                <div className="grid md:grid-cols-2 gap-4">
                  {Object.entries(SOCIAL_PLATFORMS).map(([key, platform]) => {
                    const Icon = platform.icon
                    return (
                      <Card
                        key={key}
                        className={`cursor-pointer transition-all hover:shadow-md ${
                          selectedPlatforms.includes(key) ? "ring-2 ring-green-500 bg-green-50" : ""
                        }`}
                        onClick={() => {
                          setSelectedPlatforms((prev) =>
                            prev.includes(key) ? prev.filter((p) => p !== key) : [...prev, key],
                          )
                        }}
                      >
                        <CardContent className="p-4">
                          <div className="flex items-start justify-between mb-3">
                            <div className="flex items-center space-x-3">
                              <div className={`p-2 rounded-lg ${platform.color} text-white`}>
                                <Icon className="h-5 w-5" />
                              </div>
                              <div>
                                <h4 className="font-semibold">{platform.name}</h4>
                                <p className="text-sm text-muted-foreground">{platform.description}</p>
                              </div>
                            </div>
                            {selectedPlatforms.includes(key) && <CheckCircle className="h-5 w-5 text-green-600" />}
                          </div>

                          <div className="space-y-2">
                            <div className="flex items-center justify-between text-xs">
                              <span className="text-muted-foreground">Audience:</span>
                              <span className="font-medium">{platform.audience}</span>
                            </div>
                            <div className="text-xs text-muted-foreground">
                              <strong>Best for:</strong> {platform.bestFor}
                            </div>
                            <div className="flex flex-wrap gap-1 mt-2">
                              {platform.features.slice(0, 2).map((feature) => (
                                <Badge key={feature} variant="outline" className="text-xs">
                                  {feature}
                                </Badge>
                              ))}
                              {platform.features.length > 2 && (
                                <Badge variant="outline" className="text-xs">
                                  +{platform.features.length - 2} more
                                </Badge>
                              )}
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    )
                  })}
                </div>

                {selectedPlatforms.length === 0 && (
                  <Alert>
                    <AlertDescription>Please select at least one platform to continue.</AlertDescription>
                  </Alert>
                )}
              </div>
            )}

            {/* Step 2: Connect Accounts */}
            {currentStep === 2 && (
              <div className="space-y-6">
                <div className="text-center mb-6">
                  <h3 className="text-lg font-semibold mb-2">Connect Your Accounts</h3>
                  <p className="text-muted-foreground">
                    Click connect for each platform. You'll be redirected to authorize our app.
                  </p>
                </div>

                <div className="space-y-4">
                  {selectedPlatforms.map((platformKey) => {
                    const platform = SOCIAL_PLATFORMS[platformKey as keyof typeof SOCIAL_PLATFORMS]
                    const Icon = platform.icon
                    const isConnected = connected.includes(platformKey)
                    const isConnecting = connecting === platformKey

                    return (
                      <Card key={platformKey}>
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                              <div className={`p-2 rounded-lg ${platform.color} text-white`}>
                                <Icon className="h-5 w-5" />
                              </div>
                              <div>
                                <h4 className="font-semibold">{platform.name}</h4>
                                <p className="text-sm text-muted-foreground">
                                  {isConnected ? "Successfully connected!" : "Ready to connect"}
                                </p>
                              </div>
                            </div>

                            <div className="flex items-center space-x-2">
                              <Button variant="outline" size="sm" onClick={() => openHelp(platformKey)}>
                                <ExternalLink className="h-4 w-4 mr-1" />
                                Help
                              </Button>

                              {isConnected ? (
                                <Button size="sm" disabled className="bg-green-600">
                                  <CheckCircle className="h-4 w-4 mr-2" />
                                  Connected
                                </Button>
                              ) : (
                                <Button
                                  size="sm"
                                  onClick={() => connectPlatform(platformKey)}
                                  disabled={isConnecting}
                                  className={platform.color}
                                >
                                  {isConnecting ? (
                                    <>
                                      <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-background border-t-transparent" />
                                      Connecting...
                                    </>
                                  ) : (
                                    <>
                                      <Icon className="h-4 w-4 mr-2" />
                                      Connect
                                    </>
                                  )}
                                </Button>
                              )}
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    )
                  })}
                </div>

                {connected.length > 0 && connected.length < selectedPlatforms.length && (
                  <Alert>
                    <CheckCircle className="h-4 w-4" />
                    <AlertDescription>
                      Great! You've connected {connected.length} of {selectedPlatforms.length} platforms. Connect the
                      remaining ones or continue to finish setup.
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            )}

            {/* Step 3: Complete */}
            {currentStep === 3 && (
              <div className="text-center space-y-6">
                <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                  <CheckCircle className="h-8 w-8 text-green-600" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold mb-2">You're All Connected! 🎉</h3>
                  <p className="text-muted-foreground">
                    Your social media accounts are ready for publishing content across multiple platforms.
                  </p>
                </div>

                <div className="grid md:grid-cols-2 gap-4 max-w-2xl mx-auto">
                  {connected.map((platformKey) => {
                    const platform = SOCIAL_PLATFORMS[platformKey as keyof typeof SOCIAL_PLATFORMS]
                    const Icon = platform.icon

                    return (
                      <div key={platformKey} className="flex items-center space-x-3 p-3 rounded-lg bg-gray-50">
                        <div className={`p-2 rounded ${platform.color} text-white`}>
                          <Icon className="h-4 w-4" />
                        </div>
                        <div className="flex-1 text-left">
                          <p className="font-medium">{platform.name}</p>
                          <p className="text-sm text-muted-foreground">Ready to publish</p>
                        </div>
                        <CheckCircle className="h-5 w-5 text-green-600" />
                      </div>
                    )
                  })}
                </div>

                <Alert>
                  <Share2 className="h-4 w-4" />
                  <AlertDescription>
                    <strong>What's Next?</strong> Create your first campaign or start posting content to all your
                    connected platforms!
                  </AlertDescription>
                </Alert>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Navigation */}
        <div className="flex items-center justify-between">
          <Button
            variant="outline"
            onClick={() => (currentStep > 0 ? setCurrentStep((prev) => prev - 1) : router.back())}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            {currentStep === 0 ? "Cancel" : "Back"}
          </Button>

          <div className="flex space-x-2">
            {currentStep < steps.length - 1 ? (
              <Button
                onClick={() => setCurrentStep((prev) => prev + 1)}
                disabled={
                  (currentStep === 1 && selectedPlatforms.length === 0) || (currentStep === 2 && connected.length === 0)
                }
              >
                Continue
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            ) : (
              <Button onClick={() => router.push("/campaigns/new")} className="bg-green-600 hover:bg-green-700">
                Create First Campaign
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            )}
          </div>
        </div>

        {/* Help Dialog */}
        <Dialog open={helpDialogOpen} onOpenChange={setHelpDialogOpen}>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>
                {selectedPlatformHelp &&
                  SOCIAL_PLATFORMS[selectedPlatformHelp as keyof typeof SOCIAL_PLATFORMS]?.setup.title}
              </DialogTitle>
              <DialogDescription>Follow these steps to connect your account:</DialogDescription>
            </DialogHeader>
            {selectedPlatformHelp && (
              <div className="space-y-4">
                <ol className="space-y-2">
                  {SOCIAL_PLATFORMS[selectedPlatformHelp as keyof typeof SOCIAL_PLATFORMS]?.setup.steps.map(
                    (step, index) => (
                      <li key={index} className="flex items-start space-x-2">
                        <span className="flex-shrink-0 w-6 h-6 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-sm font-medium">
                          {index + 1}
                        </span>
                        <span className="text-sm">{step}</span>
                      </li>
                    ),
                  )}
                </ol>
                <Alert>
                  <Shield className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Security Note:</strong> We use OAuth 2.0 for secure authentication. We never see your
                    password.
                  </AlertDescription>
                </Alert>
              </div>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </div>
  )
}

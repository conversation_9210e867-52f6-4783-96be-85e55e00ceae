"use client"

import { useState } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import {
  BarChart3,
  Bot,
  Calendar,
  FileText,
  Home,
  MessageSquare,
  Settings,
  Share2,
  Target,
  Users,
  Zap,
  ChevronLeft,
  ChevronRight,
} from "lucide-react"

const navigation = [
  {
    name: "Dashboard",
    href: "/dashboard",
    icon: Home,
    description: "Overview and analytics",
  },
  {
    name: "Campaigns",
    href: "/campaigns",
    icon: Target,
    description: "Marketing campaigns",
  },
  {
    name: "Posts",
    href: "/posts",
    icon: FileText,
    description: "Content management",
  },
  {
    name: "AI Studio",
    href: "/ai-studio",
    icon: Bo<PERSON>,
    description: "AI content generation",
  },
  {
    name: "Analytics",
    href: "/analytics",
    icon: Bar<PERSON>hart3,
    description: "Performance insights",
  },
  {
    name: "Social Accounts",
    href: "/social-accounts",
    icon: Share2,
    description: "Connected platforms",
  },
  {
    name: "AI Providers",
    href: "/ai-providers",
    icon: Zap,
    description: "AI model settings",
  },
  {
    name: "Team",
    href: "/team",
    icon: Users,
    description: "Team management",
  },
  {
    name: "Schedule",
    href: "/schedule",
    icon: Calendar,
    description: "Content calendar",
  },
  {
    name: "Messages",
    href: "/messages",
    icon: MessageSquare,
    description: "Notifications",
  },
]

const bottomNavigation = [
  {
    name: "Settings",
    href: "/settings",
    icon: Settings,
    description: "Account settings",
  },
]

export function Sidebar() {
  const [collapsed, setCollapsed] = useState(false)
  const pathname = usePathname()

  return (
    <div
      className={cn(
        "flex flex-col bg-card border-r border-border transition-all duration-300",
        collapsed ? "w-16" : "w-64",
      )}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        {!collapsed && (
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
              <Bot className="h-5 w-5 text-primary-foreground" />
            </div>
            <div>
              <h1 className="text-lg font-semibold">AI Marketing</h1>
              <p className="text-xs text-muted-foreground">Platform</p>
            </div>
          </div>
        )}
        <Button variant="ghost" size="sm" onClick={() => setCollapsed(!collapsed)} className="h-8 w-8 p-0">
          {collapsed ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
        </Button>
      </div>

      {/* Navigation */}
      <ScrollArea className="flex-1 px-3 py-4">
        <nav className="space-y-1">
          {navigation.map((item) => {
            const isActive = pathname === item.href || pathname.startsWith(item.href + "/")
            return (
              <Link key={item.name} href={item.href}>
                <Button
                  variant={isActive ? "secondary" : "ghost"}
                  className={cn(
                    "w-full justify-start h-10",
                    collapsed ? "px-2" : "px-3",
                    isActive && "bg-secondary text-secondary-foreground",
                  )}
                  title={collapsed ? item.name : undefined}
                >
                  <item.icon className={cn("h-4 w-4", collapsed ? "" : "mr-3")} />
                  {!collapsed && (
                    <div className="flex-1 text-left">
                      <div className="text-sm font-medium">{item.name}</div>
                      <div className="text-xs text-muted-foreground">{item.description}</div>
                    </div>
                  )}
                </Button>
              </Link>
            )
          })}
        </nav>
      </ScrollArea>

      <Separator />

      {/* Bottom Navigation */}
      <div className="p-3">
        {bottomNavigation.map((item) => {
          const isActive = pathname === item.href || pathname.startsWith(item.href + "/")
          return (
            <Link key={item.name} href={item.href}>
              <Button
                variant={isActive ? "secondary" : "ghost"}
                className={cn(
                  "w-full justify-start h-10",
                  collapsed ? "px-2" : "px-3",
                  isActive && "bg-secondary text-secondary-foreground",
                )}
                title={collapsed ? item.name : undefined}
              >
                <item.icon className={cn("h-4 w-4", collapsed ? "" : "mr-3")} />
                {!collapsed && (
                  <div className="flex-1 text-left">
                    <div className="text-sm font-medium">{item.name}</div>
                    <div className="text-xs text-muted-foreground">{item.description}</div>
                  </div>
                )}
              </Button>
            </Link>
          )
        })}
      </div>
    </div>
  )
}

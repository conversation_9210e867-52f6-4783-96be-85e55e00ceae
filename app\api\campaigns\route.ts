import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { z } from "zod"

const createCampaignSchema = z.object({
  name: z.string().min(1).max(255),
  description: z.string().optional(),
  campaignType: z.string().min(1),
  budget: z.number().positive().optional(),
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
  targetAudience: z
    .object({
      demographics: z
        .object({
          ageRange: z.string().optional(),
          gender: z.string().optional(),
          interests: z.array(z.string()).default([]),
          locations: z.array(z.string()).default([]),
        })
        .optional(),
      platforms: z.array(z.string()).default([]),
    })
    .optional(),
  settings: z.record(z.any()).default({}),
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.tenantId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = Number.parseInt(searchParams.get("page") || "1")
    const limit = Number.parseInt(searchParams.get("limit") || "10")
    const status = searchParams.get("status")
    const type = searchParams.get("type")

    const where: any = {
      tenantId: session.user.tenantId,
    }

    if (status) {
      where.status = status
    }

    if (type) {
      where.campaignType = type
    }

    const [campaigns, total] = await Promise.all([
      prisma.campaign.findMany({
        where,
        include: {
          user: {
            select: { name: true, email: true },
          },
          _count: {
            select: { posts: true },
          },
        },
        orderBy: { createdAt: "desc" },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.campaign.count({ where }),
    ])

    return NextResponse.json({
      campaigns,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error("Failed to fetch campaigns:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.tenantId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = createCampaignSchema.parse(body)

    const campaign = await prisma.campaign.create({
      data: {
        tenantId: session.user.tenantId,
        userId: session.user.id,
        name: validatedData.name,
        description: validatedData.description,
        campaignType: validatedData.campaignType,
        status: "draft",
        budget: validatedData.budget || 0,
        spent: 0,
        startDate: validatedData.startDate ? new Date(validatedData.startDate) : new Date(),
        endDate: validatedData.endDate
          ? new Date(validatedData.endDate)
          : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        targetAudience: validatedData.targetAudience || {
          demographics: {
            ageRange: "",
            gender: "",
            interests: [],
            locations: [],
          },
          platforms: [],
        },
        settings: validatedData.settings,
        metrics: {
          impressions: 0,
          clicks: 0,
          conversions: 0,
          engagement: 0,
        },
      },
      include: {
        user: {
          select: { name: true, email: true },
        },
      },
    })

    // Create initial analytics events
    await prisma.analyticsEvent.create({
      data: {
        tenantId: session.user.tenantId,
        eventType: "campaign_created",
        platform: "system",
        value: 1,
        metadata: {
          campaignId: campaign.id,
          campaignType: campaign.campaignType,
          userId: session.user.id,
        },
      },
    })

    return NextResponse.json(campaign, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: "Validation error", details: error.errors }, { status: 400 })
    }

    console.error("Failed to create campaign:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import {
  ArrowLeft,
  ArrowRight,
  Target,
  Users,
  CalendarIcon,
  Sparkles,
  CheckCircle,
  Wand2,
  Globe,
  TrendingUp,
  DollarSign,
} from "lucide-react"
import { format } from "date-fns"
import { cn } from "@/lib/utils"
import { useToast } from "@/hooks/use-toast"

const CAMPAIGN_TEMPLATES = {
  product_launch: {
    name: "Product Launch",
    icon: "🚀",
    description: "Launch a new product with maximum impact",
    suggestedBudget: 5000,
    duration: 30,
    platforms: ["facebook", "instagram", "twitter", "linkedin"],
    contentTypes: ["announcement", "behind_scenes", "testimonials", "features"],
    aiPrompts: {
      announcement: "Create an exciting product launch announcement that highlights key benefits and creates urgency",
      behind_scenes: "Write behind-the-scenes content showing the product development process",
      testimonials: "Generate customer testimonial-style content highlighting product benefits",
      features: "Create feature-focused content that explains product capabilities",
    },
  },
  brand_awareness: {
    name: "Brand Awareness",
    icon: "🎯",
    description: "Build brand recognition and reach new audiences",
    suggestedBudget: 3000,
    duration: 60,
    platforms: ["facebook", "instagram", "twitter"],
    contentTypes: ["brand_story", "values", "community", "lifestyle"],
    aiPrompts: {
      brand_story: "Tell our brand story in an engaging way that connects with our target audience",
      values: "Create content that showcases our brand values and mission",
      community: "Write community-focused content that encourages engagement and builds relationships",
      lifestyle: "Create lifestyle content that shows how our brand fits into customers' daily lives",
    },
  },
  lead_generation: {
    name: "Lead Generation",
    icon: "📈",
    description: "Generate qualified leads and grow your customer base",
    suggestedBudget: 4000,
    duration: 45,
    platforms: ["linkedin", "facebook", "twitter"],
    contentTypes: ["educational", "case_studies", "free_resources", "webinars"],
    aiPrompts: {
      educational: "Create educational content that provides value while showcasing our expertise",
      case_studies: "Write compelling case studies that demonstrate our success stories",
      free_resources: "Promote free resources like guides, templates, or tools to capture leads",
      webinars: "Create webinar promotion content that highlights the value attendees will receive",
    },
  },
  engagement: {
    name: "Engagement Boost",
    icon: "💬",
    description: "Increase engagement and build community",
    suggestedBudget: 2000,
    duration: 30,
    platforms: ["instagram", "facebook", "twitter"],
    contentTypes: ["questions", "polls", "user_generated", "contests"],
    aiPrompts: {
      questions: "Create engaging questions that encourage audience participation and discussion",
      polls: "Write poll content that sparks debate and gets people voting and commenting",
      user_generated: "Create content that encourages users to share their own experiences",
      contests: "Design contest content that drives participation and sharing",
    },
  },
  seasonal: {
    name: "Seasonal Campaign",
    icon: "🎄",
    description: "Capitalize on seasonal trends and holidays",
    suggestedBudget: 3500,
    duration: 21,
    platforms: ["facebook", "instagram", "twitter", "linkedin"],
    contentTypes: ["holiday_themed", "seasonal_offers", "gift_guides", "year_end"],
    aiPrompts: {
      holiday_themed: "Create festive holiday content that connects our brand with seasonal celebrations",
      seasonal_offers: "Write compelling seasonal offers and promotions with urgency",
      gift_guides: "Create gift guide content featuring our products for the holiday season",
      year_end: "Write year-end reflection content that builds emotional connection",
    },
  },
}

const AUDIENCE_PERSONAS = {
  young_professionals: {
    name: "Young Professionals",
    icon: "👔",
    description: "Ages 25-35, career-focused, tech-savvy",
    interests: ["career growth", "productivity", "technology", "networking"],
    platforms: ["linkedin", "instagram", "twitter"],
    tone: "professional yet approachable",
  },
  parents: {
    name: "Parents",
    icon: "👨‍👩‍👧‍👦",
    description: "Ages 30-45, family-focused, value-conscious",
    interests: ["family", "education", "health", "savings"],
    platforms: ["facebook", "instagram"],
    tone: "warm and understanding",
  },
  students: {
    name: "Students",
    icon: "🎓",
    description: "Ages 18-25, budget-conscious, social",
    interests: ["education", "entertainment", "social causes", "technology"],
    platforms: ["instagram", "twitter", "tiktok"],
    tone: "casual and energetic",
  },
  entrepreneurs: {
    name: "Entrepreneurs",
    icon: "🚀",
    description: "Ages 25-50, business-minded, growth-focused",
    interests: ["business", "innovation", "leadership", "networking"],
    platforms: ["linkedin", "twitter"],
    tone: "inspiring and authoritative",
  },
  seniors: {
    name: "Seniors",
    icon: "👴",
    description: "Ages 55+, experience-rich, community-focused",
    interests: ["health", "family", "hobbies", "community"],
    platforms: ["facebook"],
    tone: "respectful and clear",
  },
}

export default function CampaignWizard() {
  const router = useRouter()
  const { toast } = useToast()

  const [currentStep, setCurrentStep] = useState(0)
  const [loading, setLoading] = useState(false)

  const [campaignData, setCampaignData] = useState({
    // Step 1: Template Selection
    template: "",
    name: "",
    description: "",

    // Step 2: Audience & Goals
    targetAudience: "",
    customAudience: {
      ageRange: "",
      interests: [] as string[],
      locations: [] as string[],
    },
    goals: [] as string[],

    // Step 3: Platforms & Content
    platforms: [] as string[],
    contentTypes: [] as string[],

    // Step 4: Budget & Timeline
    budget: "",
    startDate: undefined as Date | undefined,
    endDate: undefined as Date | undefined,

    // Step 5: AI Configuration
    aiSettings: {
      tone: "friendly",
      contentFrequency: "daily",
      autoGenerate: true,
    },
  })

  const [currentInterest, setCurrentInterest] = useState("")
  const [currentLocation, setCurrentLocation] = useState("")
  const [generatingContent, setGeneratingContent] = useState(false)

  const steps = [
    {
      title: "Choose Template",
      description: "Select a campaign template that matches your goals",
      icon: Target,
    },
    {
      title: "Define Audience",
      description: "Who do you want to reach with this campaign?",
      icon: Users,
    },
    {
      title: "Select Platforms",
      description: "Choose where to publish your content",
      icon: Globe,
    },
    {
      title: "Set Budget & Timeline",
      description: "Define your budget and campaign duration",
      icon: CalendarIcon,
    },
    {
      title: "AI Configuration",
      description: "Configure AI to generate perfect content",
      icon: Sparkles,
    },
    {
      title: "Review & Launch",
      description: "Review your campaign and launch it",
      icon: CheckCircle,
    },
  ]

  const selectedTemplate = campaignData.template
    ? CAMPAIGN_TEMPLATES[campaignData.template as keyof typeof CAMPAIGN_TEMPLATES]
    : null
  const selectedPersona = campaignData.targetAudience
    ? AUDIENCE_PERSONAS[campaignData.targetAudience as keyof typeof AUDIENCE_PERSONAS]
    : null

  async function generateCampaignContent() {
    if (!selectedTemplate || !selectedPersona) return

    setGeneratingContent(true)
    try {
      // Simulate AI content generation
      await new Promise((resolve) => setTimeout(resolve, 2000))

      toast({
        title: "Content Generated! ✨",
        description: "AI has created a complete content strategy for your campaign",
      })
    } catch (error) {
      toast({
        title: "Generation Failed",
        description: "Unable to generate content. Please try again.",
        variant: "destructive",
      })
    } finally {
      setGeneratingContent(false)
    }
  }

  async function createCampaign() {
    setLoading(true)

    try {
      const response = await fetch("/api/campaigns", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          name: campaignData.name,
          description: campaignData.description,
          campaignType: campaignData.template,
          budget: campaignData.budget ? Number.parseFloat(campaignData.budget) : undefined,
          startDate: campaignData.startDate?.toISOString(),
          endDate: campaignData.endDate?.toISOString(),
          targetAudience: {
            persona: campaignData.targetAudience,
            demographics: campaignData.customAudience,
            platforms: campaignData.platforms,
          },
          settings: {
            aiSettings: campaignData.aiSettings,
            contentTypes: campaignData.contentTypes,
            goals: campaignData.goals,
          },
        }),
      })

      if (response.ok) {
        const campaign = await response.json()
        toast({
          title: "🎉 Campaign Created!",
          description: "Your campaign is ready. Let's start creating content!",
        })
        router.push(`/campaigns/${campaign.id}`)
      } else {
        throw new Error("Failed to create campaign")
      }
    } catch (error) {
      toast({
        title: "Creation Failed",
        description: "Unable to create campaign. Please try again.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  function addInterest() {
    if (currentInterest.trim() && !campaignData.customAudience.interests.includes(currentInterest.trim())) {
      setCampaignData((prev) => ({
        ...prev,
        customAudience: {
          ...prev.customAudience,
          interests: [...prev.customAudience.interests, currentInterest.trim()],
        },
      }))
      setCurrentInterest("")
    }
  }

  function addLocation() {
    if (currentLocation.trim() && !campaignData.customAudience.locations.includes(currentLocation.trim())) {
      setCampaignData((prev) => ({
        ...prev,
        customAudience: {
          ...prev.customAudience,
          locations: [...prev.customAudience.locations, currentLocation.trim()],
        },
      }))
      setCurrentLocation("")
    }
  }

  const progress = ((currentStep + 1) / steps.length) * 100
  const canContinue = {
    0: campaignData.template && campaignData.name,
    1: campaignData.targetAudience,
    2: campaignData.platforms.length > 0,
    3: campaignData.budget && campaignData.startDate,
    4: true,
    5: true,
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-pink-100 p-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <Target className="h-8 w-8 text-purple-600" />
            <h1 className="text-3xl font-bold">Campaign Wizard</h1>
          </div>
          <Progress value={progress} className="w-full max-w-md mx-auto" />
          <p className="text-muted-foreground mt-2">
            Step {currentStep + 1} of {steps.length}: {steps[currentStep].title}
          </p>
        </div>

        {/* Step Content */}
        <Card className="mb-8">
          <CardHeader className="text-center">
            <CardTitle className="flex items-center justify-center space-x-2">
              {steps[currentStep].icon({ className: "h-6 w-6 text-purple-600" })}
              <span>{steps[currentStep].title}</span>
            </CardTitle>
            <CardDescription>{steps[currentStep].description}</CardDescription>
          </CardHeader>
          <CardContent>
            {/* Step 0: Template Selection */}
            {currentStep === 0 && (
              <div className="space-y-6">
                <div className="grid md:grid-cols-2 gap-4">
                  {Object.entries(CAMPAIGN_TEMPLATES).map(([key, template]) => (
                    <Card
                      key={key}
                      className={`cursor-pointer transition-all hover:shadow-md ${
                        campaignData.template === key ? "ring-2 ring-purple-500 bg-purple-50" : ""
                      }`}
                      onClick={() => {
                        setCampaignData((prev) => ({
                          ...prev,
                          template: key,
                          platforms: template.platforms,
                          contentTypes: template.contentTypes,
                          budget: template.suggestedBudget.toString(),
                          endDate: new Date(Date.now() + template.duration * 24 * 60 * 60 * 1000),
                        }))
                      }}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-start space-x-3">
                          <div className="text-2xl">{template.icon}</div>
                          <div className="flex-1">
                            <h4 className="font-semibold flex items-center space-x-2">
                              <span>{template.name}</span>
                              {campaignData.template === key && <CheckCircle className="h-4 w-4 text-purple-600" />}
                            </h4>
                            <p className="text-sm text-muted-foreground mb-2">{template.description}</p>
                            <div className="flex items-center space-x-2 text-xs">
                              <Badge variant="outline">${template.suggestedBudget}</Badge>
                              <Badge variant="outline">{template.duration} days</Badge>
                              <Badge variant="outline">{template.platforms.length} platforms</Badge>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>

                {campaignData.template && (
                  <div className="space-y-4">
                    <div className="grid gap-4 md:grid-cols-2">
                      <div className="space-y-2">
                        <Label htmlFor="campaign-name">Campaign Name *</Label>
                        <Input
                          id="campaign-name"
                          value={campaignData.name}
                          onChange={(e) => setCampaignData((prev) => ({ ...prev, name: e.target.value }))}
                          placeholder={`${selectedTemplate?.name} Campaign`}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>Suggested Budget</Label>
                        <div className="flex items-center space-x-2">
                          <DollarSign className="h-4 w-4 text-muted-foreground" />
                          <span className="text-lg font-semibold">
                            {selectedTemplate?.suggestedBudget.toLocaleString()}
                          </span>
                          <Badge variant="secondary">Recommended</Badge>
                        </div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="campaign-description">Description</Label>
                      <Textarea
                        id="campaign-description"
                        value={campaignData.description}
                        onChange={(e) => setCampaignData((prev) => ({ ...prev, description: e.target.value }))}
                        placeholder={`Describe your ${selectedTemplate?.name.toLowerCase()} goals and strategy...`}
                        rows={3}
                      />
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Step 1: Audience Selection */}
            {currentStep === 1 && (
              <div className="space-y-6">
                <div className="text-center mb-6">
                  <h3 className="text-lg font-semibold mb-2">Choose Your Target Audience</h3>
                  <p className="text-muted-foreground">Select a persona or create a custom audience</p>
                </div>

                <Tabs defaultValue="personas" className="w-full">
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="personas">Audience Personas</TabsTrigger>
                    <TabsTrigger value="custom">Custom Audience</TabsTrigger>
                  </TabsList>

                  <TabsContent value="personas" className="space-y-4">
                    <div className="grid md:grid-cols-2 gap-4">
                      {Object.entries(AUDIENCE_PERSONAS).map(([key, persona]) => (
                        <Card
                          key={key}
                          className={`cursor-pointer transition-all hover:shadow-md ${
                            campaignData.targetAudience === key ? "ring-2 ring-purple-500 bg-purple-50" : ""
                          }`}
                          onClick={() => setCampaignData((prev) => ({ ...prev, targetAudience: key }))}
                        >
                          <CardContent className="p-4">
                            <div className="flex items-start space-x-3">
                              <div className="text-2xl">{persona.icon}</div>
                              <div className="flex-1">
                                <h4 className="font-semibold flex items-center space-x-2">
                                  <span>{persona.name}</span>
                                  {campaignData.targetAudience === key && (
                                    <CheckCircle className="h-4 w-4 text-purple-600" />
                                  )}
                                </h4>
                                <p className="text-sm text-muted-foreground mb-2">{persona.description}</p>
                                <div className="space-y-1">
                                  <div className="flex flex-wrap gap-1">
                                    {persona.interests.slice(0, 3).map((interest) => (
                                      <Badge key={interest} variant="outline" className="text-xs">
                                        {interest}
                                      </Badge>
                                    ))}
                                  </div>
                                  <p className="text-xs text-muted-foreground">
                                    <strong>Tone:</strong> {persona.tone}
                                  </p>
                                </div>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </TabsContent>

                  <TabsContent value="custom" className="space-y-4">
                    <div className="grid gap-4 md:grid-cols-2">
                      <div className="space-y-2">
                        <Label>Age Range</Label>
                        <Select
                          value={campaignData.customAudience.ageRange}
                          onValueChange={(value) =>
                            setCampaignData((prev) => ({
                              ...prev,
                              customAudience: { ...prev.customAudience, ageRange: value },
                            }))
                          }
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select age range" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="18-24">18-24</SelectItem>
                            <SelectItem value="25-34">25-34</SelectItem>
                            <SelectItem value="35-44">35-44</SelectItem>
                            <SelectItem value="45-54">45-54</SelectItem>
                            <SelectItem value="55-64">55-64</SelectItem>
                            <SelectItem value="65+">65+</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>Interests</Label>
                      <div className="flex space-x-2">
                        <Input
                          value={currentInterest}
                          onChange={(e) => setCurrentInterest(e.target.value)}
                          placeholder="Add interest (e.g., technology, fitness)"
                          onKeyPress={(e) => e.key === "Enter" && (e.preventDefault(), addInterest())}
                        />
                        <Button type="button" onClick={addInterest} variant="outline">
                          Add
                        </Button>
                      </div>
                      <div className="flex flex-wrap gap-2 mt-2">
                        {campaignData.customAudience.interests.map((interest) => (
                          <Badge
                            key={interest}
                            variant="secondary"
                            className="cursor-pointer"
                            onClick={() =>
                              setCampaignData((prev) => ({
                                ...prev,
                                customAudience: {
                                  ...prev.customAudience,
                                  interests: prev.customAudience.interests.filter((i) => i !== interest),
                                },
                              }))
                            }
                          >
                            {interest} ×
                          </Badge>
                        ))}
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>Locations</Label>
                      <div className="flex space-x-2">
                        <Input
                          value={currentLocation}
                          onChange={(e) => setCurrentLocation(e.target.value)}
                          placeholder="Add location (e.g., United States, London)"
                          onKeyPress={(e) => e.key === "Enter" && (e.preventDefault(), addLocation())}
                        />
                        <Button type="button" onClick={addLocation} variant="outline">
                          Add
                        </Button>
                      </div>
                      <div className="flex flex-wrap gap-2 mt-2">
                        {campaignData.customAudience.locations.map((location) => (
                          <Badge
                            key={location}
                            variant="secondary"
                            className="cursor-pointer"
                            onClick={() =>
                              setCampaignData((prev) => ({
                                ...prev,
                                customAudience: {
                                  ...prev.customAudience,
                                  locations: prev.customAudience.locations.filter((l) => l !== location),
                                },
                              }))
                            }
                          >
                            {location} ×
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>
              </div>
            )}

            {/* Step 2: Platform Selection */}
            {currentStep === 2 && (
              <div className="space-y-6">
                <div className="text-center mb-6">
                  <h3 className="text-lg font-semibold mb-2">Select Publishing Platforms</h3>
                  <p className="text-muted-foreground">
                    {selectedTemplate &&
                      `Recommended for ${selectedTemplate.name}: ${selectedTemplate.platforms.join(", ")}`}
                  </p>
                </div>

                <div className="grid md:grid-cols-2 gap-4">
                  {[
                    {
                      id: "facebook",
                      name: "Facebook",
                      icon: "📘",
                      audience: "2.9B users",
                      bestFor: "Brand awareness",
                    },
                    { id: "instagram", name: "Instagram", icon: "📷", audience: "2B users", bestFor: "Visual content" },
                    {
                      id: "twitter",
                      name: "Twitter/X",
                      icon: "🐦",
                      audience: "450M users",
                      bestFor: "Real-time updates",
                    },
                    { id: "linkedin", name: "LinkedIn", icon: "💼", audience: "900M users", bestFor: "B2B marketing" },
                    { id: "youtube", name: "YouTube", icon: "📺", audience: "2.7B users", bestFor: "Video content" },
                    { id: "tiktok", name: "TikTok", icon: "🎵", audience: "1B users", bestFor: "Gen Z engagement" },
                  ].map((platform) => (
                    <Card
                      key={platform.id}
                      className={`cursor-pointer transition-all hover:shadow-md ${
                        campaignData.platforms.includes(platform.id) ? "ring-2 ring-purple-500 bg-purple-50" : ""
                      }`}
                      onClick={() => {
                        setCampaignData((prev) => ({
                          ...prev,
                          platforms: prev.platforms.includes(platform.id)
                            ? prev.platforms.filter((p) => p !== platform.id)
                            : [...prev.platforms, platform.id],
                        }))
                      }}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="text-2xl">{platform.icon}</div>
                            <div>
                              <h4 className="font-semibold">{platform.name}</h4>
                              <p className="text-sm text-muted-foreground">{platform.audience}</p>
                              <p className="text-xs text-muted-foreground">Best for: {platform.bestFor}</p>
                            </div>
                          </div>
                          {campaignData.platforms.includes(platform.id) && (
                            <CheckCircle className="h-5 w-5 text-purple-600" />
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>

                {selectedPersona && (
                  <Alert>
                    <TrendingUp className="h-4 w-4" />
                    <AlertDescription>
                      <strong>Recommendation:</strong> Based on your target audience ({selectedPersona.name}), we
                      suggest focusing on: {selectedPersona.platforms.join(", ")}
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            )}

            {/* Step 3: Budget & Timeline */}
            {currentStep === 3 && (
              <div className="space-y-6">
                <div className="grid gap-6 md:grid-cols-2">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center space-x-2">
                        <DollarSign className="h-5 w-5" />
                        <span>Budget</span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="budget">Total Budget ($)</Label>
                        <Input
                          id="budget"
                          type="number"
                          value={campaignData.budget}
                          onChange={(e) => setCampaignData((prev) => ({ ...prev, budget: e.target.value }))}
                          placeholder="5000"
                        />
                      </div>
                      {selectedTemplate && (
                        <div className="p-3 bg-blue-50 rounded-lg">
                          <p className="text-sm">
                            <strong>Suggested:</strong> ${selectedTemplate.suggestedBudget.toLocaleString()}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            Based on {selectedTemplate.name.toLowerCase()} best practices
                          </p>
                        </div>
                      )}
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center space-x-2">
                        <CalendarIcon className="h-5 w-5" />
                        <span>Timeline</span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid gap-4 md:grid-cols-2">
                        <div className="space-y-2">
                          <Label>Start Date</Label>
                          <Popover>
                            <PopoverTrigger asChild>
                              <Button
                                variant="outline"
                                className={cn(
                                  "w-full justify-start text-left font-normal",
                                  !campaignData.startDate && "text-muted-foreground",
                                )}
                              >
                                <CalendarIcon className="mr-2 h-4 w-4" />
                                {campaignData.startDate ? format(campaignData.startDate, "PPP") : "Pick date"}
                              </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0">
                              <Calendar
                                mode="single"
                                selected={campaignData.startDate}
                                onSelect={(date) => setCampaignData((prev) => ({ ...prev, startDate: date }))}
                                initialFocus
                              />
                            </PopoverContent>
                          </Popover>
                        </div>
                        <div className="space-y-2">
                          <Label>End Date</Label>
                          <Popover>
                            <PopoverTrigger asChild>
                              <Button
                                variant="outline"
                                className={cn(
                                  "w-full justify-start text-left font-normal",
                                  !campaignData.endDate && "text-muted-foreground",
                                )}
                              >
                                <CalendarIcon className="mr-2 h-4 w-4" />
                                {campaignData.endDate ? format(campaignData.endDate, "PPP") : "Pick date"}
                              </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0">
                              <Calendar
                                mode="single"
                                selected={campaignData.endDate}
                                onSelect={(date) => setCampaignData((prev) => ({ ...prev, endDate: date }))}
                                initialFocus
                              />
                            </PopoverContent>
                          </Popover>
                        </div>
                      </div>
                      {selectedTemplate && (
                        <div className="p-3 bg-green-50 rounded-lg">
                          <p className="text-sm">
                            <strong>Suggested Duration:</strong> {selectedTemplate.duration} days
                          </p>
                          <p className="text-xs text-muted-foreground">
                            Optimal for {selectedTemplate.name.toLowerCase()} campaigns
                          </p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </div>
              </div>
            )}

            {/* Step 4: AI Configuration */}
            {currentStep === 4 && (
              <div className="space-y-6">
                <div className="text-center mb-6">
                  <h3 className="text-lg font-semibold mb-2">Configure AI Content Generation</h3>
                  <p className="text-muted-foreground">Set up AI to create perfect content for your campaign</p>
                </div>

                <div className="grid gap-6 md:grid-cols-2">
                  <Card>
                    <CardHeader>
                      <CardTitle>Content Settings</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="space-y-2">
                        <Label>Content Tone</Label>
                        <Select
                          value={campaignData.aiSettings.tone}
                          onValueChange={(value) =>
                            setCampaignData((prev) => ({
                              ...prev,
                              aiSettings: { ...prev.aiSettings, tone: value },
                            }))
                          }
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="professional">Professional</SelectItem>
                            <SelectItem value="friendly">Friendly</SelectItem>
                            <SelectItem value="casual">Casual</SelectItem>
                            <SelectItem value="formal">Formal</SelectItem>
                            <SelectItem value="creative">Creative</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label>Content Frequency</Label>
                        <Select
                          value={campaignData.aiSettings.contentFrequency}
                          onValueChange={(value) =>
                            setCampaignData((prev) => ({
                              ...prev,
                              aiSettings: { ...prev.aiSettings, contentFrequency: value },
                            }))
                          }
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="daily">Daily</SelectItem>
                            <SelectItem value="every-other-day">Every Other Day</SelectItem>
                            <SelectItem value="weekly">Weekly</SelectItem>
                            <SelectItem value="bi-weekly">Bi-weekly</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="auto-generate"
                          checked={campaignData.aiSettings.autoGenerate}
                          onCheckedChange={(checked) =>
                            setCampaignData((prev) => ({
                              ...prev,
                              aiSettings: { ...prev.aiSettings, autoGenerate: checked as boolean },
                            }))
                          }
                        />
                        <Label htmlFor="auto-generate" className="text-sm">
                          Auto-generate content based on schedule
                        </Label>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Content Preview</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <Button onClick={generateCampaignContent} disabled={generatingContent} className="w-full">
                          {generatingContent ? (
                            <>
                              <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-background border-t-transparent" />
                              Generating Preview...
                            </>
                          ) : (
                            <>
                              <Wand2 className="mr-2 h-4 w-4" />
                              Generate Content Preview
                            </>
                          )}
                        </Button>

                        {selectedTemplate && selectedPersona && (
                          <div className="p-3 bg-gray-50 rounded-lg">
                            <p className="text-sm font-medium mb-2">AI will create:</p>
                            <ul className="text-xs space-y-1">
                              {selectedTemplate.contentTypes.map((type) => (
                                <li key={type} className="flex items-center space-x-2">
                                  <CheckCircle className="h-3 w-3 text-green-600" />
                                  <span className="capitalize">{type.replace("_", " ")}</span>
                                </li>
                              ))}
                            </ul>
                            <p className="text-xs text-muted-foreground mt-2">
                              Optimized for {selectedPersona.name} with {campaignData.aiSettings.tone} tone
                            </p>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            )}

            {/* Step 5: Review & Launch */}
            {currentStep === 5 && (
              <div className="space-y-6">
                <div className="text-center mb-6">
                  <h3 className="text-xl font-semibold mb-2">Review Your Campaign</h3>
                  <p className="text-muted-foreground">Everything looks good? Let's launch your campaign!</p>
                </div>

                <div className="grid gap-4 md:grid-cols-2">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Campaign Overview</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Template:</span>
                        <span className="font-medium">{selectedTemplate?.name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Name:</span>
                        <span className="font-medium">{campaignData.name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Budget:</span>
                        <span className="font-medium">
                          ${Number.parseFloat(campaignData.budget || "0").toLocaleString()}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Duration:</span>
                        <span className="font-medium">
                          {campaignData.startDate && campaignData.endDate
                            ? `${Math.ceil((campaignData.endDate.getTime() - campaignData.startDate.getTime()) / (1000 * 60 * 60 * 24))} days`
                            : "Not set"}
                        </span>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Target & Platforms</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div>
                        <span className="text-muted-foreground">Audience:</span>
                        <p className="font-medium">{selectedPersona?.name || "Custom Audience"}</p>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Platforms:</span>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {campaignData.platforms.map((platform) => (
                            <Badge key={platform} variant="secondary" className="capitalize">
                              {platform}
                            </Badge>
                          ))}
                        </div>
                      </div>
                      <div>
                        <span className="text-muted-foreground">AI Tone:</span>
                        <p className="font-medium capitalize">{campaignData.aiSettings.tone}</p>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <Alert>
                  <CheckCircle className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Ready to Launch!</strong> Your campaign will start generating content immediately and begin
                    publishing according to your schedule.
                  </AlertDescription>
                </Alert>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Navigation */}
        <div className="flex items-center justify-between">
          <Button
            variant="outline"
            onClick={() => (currentStep > 0 ? setCurrentStep((prev) => prev - 1) : router.back())}
            disabled={loading}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            {currentStep === 0 ? "Cancel" : "Back"}
          </Button>

          <div className="flex space-x-2">
            {currentStep < steps.length - 1 ? (
              <Button
                onClick={() => setCurrentStep((prev) => prev + 1)}
                disabled={!canContinue[currentStep as keyof typeof canContinue]}
              >
                Continue
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            ) : (
              <Button onClick={createCampaign} disabled={loading} className="bg-purple-600 hover:bg-purple-700">
                {loading ? (
                  <>
                    <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-background border-t-transparent" />
                    Creating Campaign...
                  </>
                ) : (
                  <>🚀 Launch Campaign</>
                )}
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

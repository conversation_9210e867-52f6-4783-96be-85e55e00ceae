import { S3Client, PutObjectCommand, DeleteObjectCommand, GetObjectCommand } from "@aws-sdk/client-s3"
import { getSignedUrl } from "@aws-sdk/s3-request-presigner"
import { prisma } from "./prisma"

export interface UploadResult {
  url: string
  key: string
  filename: string
  size: number
  mimeType: string
}

export class CloudStorageManager {
  private s3Client: S3Client
  private bucketName: string

  constructor() {
    this.s3Client = new S3Client({
      region: process.env.AWS_REGION || "us-east-1",
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID || "",
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || "",
      },
    })
    this.bucketName = process.env.AWS_S3_BUCKET || "ai-marketing-platform"
  }

  async uploadFile(
    file: Buffer,
    filename: string,
    mimeType: string,
    tenantId: string,
    userId: string,
  ): Promise<UploadResult> {
    try {
      // Generate unique key
      const timestamp = Date.now()
      const randomString = Math.random().toString(36).substring(2, 15)
      const extension = filename.split(".").pop()
      const key = `${tenantId}/${userId}/${timestamp}-${randomString}.${extension}`

      // Upload to S3
      const command = new PutObjectCommand({
        Bucket: this.bucketName,
        Key: key,
        Body: file,
        ContentType: mimeType,
        Metadata: {
          originalName: filename,
          tenantId,
          userId,
          uploadedAt: new Date().toISOString(),
        },
      })

      await this.s3Client.send(command)

      // Generate public URL
      const url = `https://${this.bucketName}.s3.${process.env.AWS_REGION || "us-east-1"}.amazonaws.com/${key}`

      // Save to database
      await prisma.fileUpload.create({
        data: {
          tenantId,
          userId,
          filename: key,
          originalName: filename,
          mimeType,
          size: file.length,
          url,
          metadata: {
            key,
            bucket: this.bucketName,
            uploadedAt: new Date().toISOString(),
          },
        },
      })

      return {
        url,
        key,
        filename,
        size: file.length,
        mimeType,
      }
    } catch (error) {
      console.error("Failed to upload file to S3:", error)
      throw new Error("File upload failed")
    }
  }

  async deleteFile(key: string, tenantId: string): Promise<boolean> {
    try {
      // Delete from S3
      const command = new DeleteObjectCommand({
        Bucket: this.bucketName,
        Key: key,
      })

      await this.s3Client.send(command)

      // Delete from database
      await prisma.fileUpload.deleteMany({
        where: {
          filename: key,
          tenantId,
        },
      })

      return true
    } catch (error) {
      console.error("Failed to delete file from S3:", error)
      return false
    }
  }

  async getSignedDownloadUrl(key: string, expiresIn = 3600): Promise<string> {
    try {
      const command = new GetObjectCommand({
        Bucket: this.bucketName,
        Key: key,
      })

      const signedUrl = await getSignedUrl(this.s3Client, command, { expiresIn })
      return signedUrl
    } catch (error) {
      console.error("Failed to generate signed URL:", error)
      throw new Error("Failed to generate download URL")
    }
  }

  async getSignedUploadUrl(key: string, mimeType: string, expiresIn = 3600): Promise<string> {
    try {
      const command = new PutObjectCommand({
        Bucket: this.bucketName,
        Key: key,
        ContentType: mimeType,
      })

      const signedUrl = await getSignedUrl(this.s3Client, command, { expiresIn })
      return signedUrl
    } catch (error) {
      console.error("Failed to generate signed upload URL:", error)
      throw new Error("Failed to generate upload URL")
    }
  }
}

export const cloudStorage = new CloudStorageManager()

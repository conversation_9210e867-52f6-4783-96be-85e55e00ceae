declare module "next-auth" {
  interface Session {
    user: {
      id: string
      email: string
      name: string
      image?: string
      role: string
      tenantId: string
      tenantName?: string
    }
  }

  interface User {
    id: string
    email: string
    name: string
    image?: string
    role: string
    tenantId: string
    tenantName?: string
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    role: string
    tenantId: string
    tenantName?: string
  }
}

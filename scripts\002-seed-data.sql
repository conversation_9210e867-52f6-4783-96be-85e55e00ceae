-- Insert default tenant for development
INSERT INTO tenants (id, name, subscription_tier) 
VALUES ('550e8400-e29b-41d4-a716-446655440000', 'Demo Company', 'pro')
ON CONFLICT (id) DO NOTHING;

-- Insert demo user (password: 'password123')
INSERT INTO users (id, email, password_hash, first_name, last_name, role, tenant_id) 
VALUES (
  '550e8400-e29b-41d4-a716-446655440001', 
  '<EMAIL>', 
  '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qm', 
  'Demo', 
  'User', 
  'admin', 
  '550e8400-e29b-41d4-a716-446655440000'
)
ON CONFLICT (email) DO NOTHING;

-- Insert sample AI provider configurations
INSERT INTO ai_providers (id, tenant_id, name, provider_type, api_key_encrypted, models, settings, health_status) 
VALUES 
(
  '550e8400-e29b-41d4-a716-446655440002',
  '550e8400-e29b-41d4-a716-446655440000',
  'OpenAI GPT',
  'openai',
  'encrypted_key_placeholder',
  '["gpt-4", "gpt-3.5-turbo", "gpt-4-turbo"]',
  '{"pricing": {"gpt-4": {"inputTokens": 0.00003, "outputTokens": 0.00006}, "gpt-3.5-turbo": {"inputTokens": 0.000002, "outputTokens": 0.000002}}}',
  'unknown'
),
(
  '550e8400-e29b-41d4-a716-446655440003',
  '550e8400-e29b-41d4-a716-446655440000',
  'Anthropic Claude',
  'anthropic',
  'encrypted_key_placeholder',
  '["claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307"]',
  '{"pricing": {"claude-3-opus-20240229": {"inputTokens": 0.000015, "outputTokens": 0.000075}, "claude-3-sonnet-20240229": {"inputTokens": 0.000003, "outputTokens": 0.000015}}}',
  'unknown'
)
ON CONFLICT (id) DO NOTHING;

-- Insert sample campaign
INSERT INTO campaigns (id, tenant_id, user_id, name, description, status, campaign_type, target_audience, budget, start_date, end_date) 
VALUES (
  '550e8400-e29b-41d4-a716-446655440004',
  '550e8400-e29b-41d4-a716-446655440000',
  '550e8400-e29b-41d4-a716-446655440001',
  'Product Launch Campaign',
  'Multi-platform campaign for new product launch',
  'active',
  'social_media',
  '{"demographics": {"age": "25-45", "interests": ["technology", "innovation"]}, "locations": ["US", "CA", "UK"]}',
  5000.00,
  CURRENT_TIMESTAMP,
  CURRENT_TIMESTAMP + INTERVAL '30 days'
)
ON CONFLICT (id) DO NOTHING;

-- Insert sample posts
INSERT INTO posts (id, campaign_id, tenant_id, user_id, title, content, hashtags, platforms, status, ai_generated, ai_model_used) 
VALUES 
(
  '550e8400-e29b-41d4-a716-446655440005',
  '550e8400-e29b-41d4-a716-446655440004',
  '550e8400-e29b-41d4-a716-446655440000',
  '550e8400-e29b-41d4-a716-446655440001',
  'Exciting Product Launch!',
  'We are thrilled to announce the launch of our revolutionary new product! This innovation will transform how you work and play. Stay tuned for more details! 🚀',
  '["#ProductLaunch", "#Innovation", "#Technology", "#NewProduct"]',
  '["facebook", "twitter", "linkedin"]',
  'published',
  true,
  'gpt-4'
),
(
  '550e8400-e29b-41d4-a716-446655440006',
  '550e8400-e29b-41d4-a716-446655440004',
  '550e8400-e29b-41d4-a716-446655440000',
  '550e8400-e29b-41d4-a716-446655440001',
  'Behind the Scenes',
  'Take a look behind the scenes of our product development process. Our team has been working tirelessly to bring you the best experience possible.',
  '["#BehindTheScenes", "#TeamWork", "#Development", "#Process"]',
  '["instagram", "facebook"]',
  'scheduled',
  true,
  'claude-3-sonnet-20240229'
)
ON CONFLICT (id) DO NOTHING;

-- Insert sample analytics events
INSERT INTO analytics_events (tenant_id, post_id, campaign_id, event_type, platform, value, metadata) 
VALUES 
('550e8400-e29b-41d4-a716-446655440000', '550e8400-e29b-41d4-a716-446655440005', '550e8400-e29b-41d4-a716-446655440004', 'impression', 'facebook', 1250, '{"source": "organic"}'),
('550e8400-e29b-41d4-a716-446655440000', '550e8400-e29b-41d4-a716-446655440005', '550e8400-e29b-41d4-a716-446655440004', 'like', 'facebook', 45, '{"user_demographics": {"age_range": "25-34"}}'),
('550e8400-e29b-41d4-a716-446655440000', '550e8400-e29b-41d4-a716-446655440005', '550e8400-e29b-41d4-a716-446655440004', 'share', 'facebook', 12, '{"reach_multiplier": 3.2}'),
('550e8400-e29b-41d4-a716-446655440000', '550e8400-e29b-41d4-a716-446655440005', '550e8400-e29b-41d4-a716-446655440004', 'click', 'facebook', 78, '{"click_type": "link"}'),
('550e8400-e29b-41d4-a716-446655440000', '550e8400-e29b-41d4-a716-446655440005', '550e8400-e29b-41d4-a716-446655440004', 'impression', 'twitter', 890, '{"source": "timeline"}'),
('550e8400-e29b-41d4-a716-446655440000', '550e8400-e29b-41d4-a716-446655440005', '550e8400-e29b-41d4-a716-446655440004', 'like', 'twitter', 23, '{}'),
('550e8400-e29b-41d4-a716-446655440000', '550e8400-e29b-41d4-a716-446655440005', '550e8400-e29b-41d4-a716-446655440004', 'retweet', 'twitter', 8, '{}');

-- Insert sample AI usage tracking
INSERT INTO ai_usage (tenant_id, user_id, provider, model, tokens_used, cost, request_type) 
VALUES 
('550e8400-e29b-41d4-a716-446655440000', '550e8400-e29b-41d4-a716-446655440001', 'openai', 'gpt-4', 1250, 0.0375, 'text_generation'),
('550e8400-e29b-41d4-a716-446655440000', '550e8400-e29b-41d4-a716-446655440001', 'anthropic', 'claude-3-sonnet-20240229', 890, 0.00267, 'text_generation'),
('550e8400-e29b-41d4-a716-446655440000', '550e8400-e29b-41d4-a716-446655440001', 'openai', 'gpt-3.5-turbo', 2340, 0.00468, 'text_generation');

{"name": "ai-marketing-platform", "version": "1.0.0", "private": true, "scripts": {"build": "next build", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:push": "prisma db push", "db:seed": "tsx scripts/seed.ts", "db:studio": "prisma studio", "dev": "next dev", "lint": "next lint", "start": "next start"}, "dependencies": {"@ai-sdk/anthropic": "latest", "@ai-sdk/deepseek": "latest", "@ai-sdk/google": "latest", "@ai-sdk/groq": "latest", "@ai-sdk/mistral": "latest", "@ai-sdk/openai": "latest", "@ai-sdk/xai": "latest", "@auth/core": "latest", "@aws-sdk/client-s3": "latest", "@aws-sdk/s3-request-presigner": "latest", "@hookform/resolvers": "^3.9.0", "@next-auth/prisma-adapter": "latest", "@prisma/client": "latest", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-sheet": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-tooltip": "^1.1.3", "@vercel/analytics": "^1.3.1", "@vercel/speed-insights": "^1.0.12", "ai": "latest", "bcryptjs": "latest", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "crypto": "latest", "date-fns": "latest", "lucide-react": "^0.454.0", "nanoid": "latest", "next": "15.2.4", "next-auth": "latest", "next-themes": "latest", "nodemailer": "latest", "react": "^19", "react-dom": "^19", "react-hook-form": "^7.53.1", "recharts": "latest", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "zod": "^3.23.8"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^8.57.1", "eslint-config-next": "15.0.3", "postcss": "^8.5", "prisma": "^5.20.0", "tailwindcss": "^3.4.17", "tsx": "^4.19.2", "typescript": "^5"}, "engines": {"node": ">=18.0.0"}}
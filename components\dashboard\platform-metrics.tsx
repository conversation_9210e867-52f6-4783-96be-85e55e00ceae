"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { formatNumber } from "@/lib/utils"
import { TrendingUp, TrendingDown, Users, Eye, MousePointer, Share2 } from "lucide-react"
import type { PlatformMetrics as PlatformMetricsType } from "@/lib/types"

export function PlatformMetrics() {
  const [platforms, setPlatforms] = useState<PlatformMetricsType[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    async function fetchPlatforms() {
      try {
        const response = await fetch("/api/dashboard/platform-metrics")
        if (response.ok) {
          const data = await response.json()
          setPlatforms(data.platforms)
        }
      } catch (error) {
        console.error("Failed to fetch platform metrics:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchPlatforms()
  }, [])

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Platform Performance</CardTitle>
          <CardDescription>Performance metrics across social media platforms</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {Array.from({ length: 6 }).map((_, i) => (
              <div key={i} className="border rounded-lg p-4 space-y-4">
                <div className="flex items-center justify-between">
                  <div className="h-6 w-20 bg-muted animate-pulse rounded" />
                  <div className="h-5 w-12 bg-muted animate-pulse rounded" />
                </div>
                <div className="space-y-2">
                  <div className="h-4 w-full bg-muted animate-pulse rounded" />
                  <div className="h-4 w-3/4 bg-muted animate-pulse rounded" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case "facebook":
        return "📘"
      case "instagram":
        return "📷"
      case "twitter":
        return "🐦"
      case "linkedin":
        return "💼"
      case "youtube":
        return "📺"
      case "tiktok":
        return "🎵"
      default:
        return "📱"
    }
  }

  const getPlatformColor = (platform: string) => {
    const colors: Record<string, string> = {
      facebook: "bg-blue-500",
      instagram: "bg-pink-500",
      twitter: "bg-sky-500",
      linkedin: "bg-blue-700",
      youtube: "bg-red-500",
      tiktok: "bg-black",
    }
    return colors[platform] || "bg-gray-500"
  }

  const getGrowthColor = (growth: number) => {
    if (growth > 0) return "text-green-600"
    if (growth < 0) return "text-red-600"
    return "text-gray-600"
  }

  const getGrowthIcon = (growth: number) => {
    return growth >= 0 ? (
      <TrendingUp className="h-3 w-3 text-green-600" />
    ) : (
      <TrendingDown className="h-3 w-3 text-red-600" />
    )
  }

  const totalEngagement = platforms.reduce((sum, p) => sum + p.engagement, 0)

  return (
    <Card>
      <CardHeader>
        <CardTitle>Platform Performance</CardTitle>
        <CardDescription>Performance metrics across social media platforms</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {platforms.map((platform) => (
            <div key={platform.platform} className="border rounded-lg p-4 space-y-4">
              {/* Platform Header */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <span className="text-lg">{getPlatformIcon(platform.platform)}</span>
                  <h3 className="font-semibold capitalize">{platform.platform}</h3>
                </div>
                <Badge className={`text-white ${getPlatformColor(platform.platform)}`}>{platform.posts} posts</Badge>
              </div>

              {/* Engagement Progress */}
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Engagement Share</span>
                  <span>{totalEngagement > 0 ? Math.round((platform.engagement / totalEngagement) * 100) : 0}%</span>
                </div>
                <Progress
                  value={totalEngagement > 0 ? (platform.engagement / totalEngagement) * 100 : 0}
                  className="h-2"
                />
              </div>

              {/* Metrics Grid */}
              <div className="grid grid-cols-2 gap-3 text-center">
                <div className="space-y-1">
                  <div className="flex items-center justify-center">
                    <Eye className="h-4 w-4 text-muted-foreground" />
                  </div>
                  <div className="text-lg font-semibold">{formatNumber(platform.impressions)}</div>
                  <div className="text-xs text-muted-foreground">Impressions</div>
                </div>
                <div className="space-y-1">
                  <div className="flex items-center justify-center">
                    <Share2 className="h-4 w-4 text-muted-foreground" />
                  </div>
                  <div className="text-lg font-semibold">{formatNumber(platform.engagement)}</div>
                  <div className="text-xs text-muted-foreground">Engagement</div>
                </div>
                <div className="space-y-1">
                  <div className="flex items-center justify-center">
                    <MousePointer className="h-4 w-4 text-muted-foreground" />
                  </div>
                  <div className="text-lg font-semibold">{formatNumber(platform.clicks)}</div>
                  <div className="text-xs text-muted-foreground">Clicks</div>
                </div>
                <div className="space-y-1">
                  <div className="flex items-center justify-center">
                    <Users className="h-4 w-4 text-muted-foreground" />
                  </div>
                  <div className="text-lg font-semibold">{formatNumber(platform.reach)}</div>
                  <div className="text-xs text-muted-foreground">Reach</div>
                </div>
              </div>

              {/* Footer Stats */}
              <div className="flex items-center justify-between text-sm border-t pt-3">
                <div className="flex items-center space-x-1">
                  <Users className="h-3 w-3 text-muted-foreground" />
                  <span className="text-muted-foreground">{formatNumber(platform.followers)} followers</span>
                </div>
                <div className={`flex items-center space-x-1 ${getGrowthColor(platform.growth)}`}>
                  {getGrowthIcon(platform.growth)}
                  <span className="text-xs font-medium">
                    {platform.growth > 0 ? "+" : ""}
                    {platform.growth}%
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Summary Stats */}
        <div className="mt-6 grid grid-cols-2 md:grid-cols-4 gap-4 pt-6 border-t">
          <div className="text-center">
            <div className="text-2xl font-bold">{formatNumber(platforms.reduce((sum, p) => sum + p.posts, 0))}</div>
            <div className="text-sm text-muted-foreground">Total Posts</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold">
              {formatNumber(platforms.reduce((sum, p) => sum + p.engagement, 0))}
            </div>
            <div className="text-sm text-muted-foreground">Total Engagement</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold">{formatNumber(platforms.reduce((sum, p) => sum + p.reach, 0))}</div>
            <div className="text-sm text-muted-foreground">Total Reach</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold">{formatNumber(platforms.reduce((sum, p) => sum + p.followers, 0))}</div>
            <div className="text-sm text-muted-foreground">Total Followers</div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

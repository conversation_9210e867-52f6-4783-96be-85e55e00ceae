import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.tenantId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const period = searchParams.get("period") || "30d"

    // Calculate date range
    const now = new Date()
    let startDate: Date

    switch (period) {
      case "7d":
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        break
      case "30d":
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        break
      case "90d":
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
        break
      default:
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
    }

    // Get overview metrics
    const [
      totalCampaigns,
      activeCampaigns,
      totalPosts,
      publishedPosts,
      totalSpent,
      totalImpressions,
      totalEngagement,
      recentPosts,
      campaignPerformance,
      platformMetrics,
      engagementTrend,
    ] = await Promise.all([
      // Total campaigns
      prisma.campaign.count({
        where: { tenantId: session.user.tenantId },
      }),

      // Active campaigns
      prisma.campaign.count({
        where: {
          tenantId: session.user.tenantId,
          status: "active",
        },
      }),

      // Total posts
      prisma.post.count({
        where: { tenantId: session.user.tenantId },
      }),

      // Published posts
      prisma.post.count({
        where: {
          tenantId: session.user.tenantId,
          status: "published",
        },
      }),

      // Total spent
      prisma.campaign.aggregate({
        where: { tenantId: session.user.tenantId },
        _sum: { spent: true },
      }),

      // Total impressions from analytics events
      prisma.analyticsEvent.aggregate({
        where: {
          tenantId: session.user.tenantId,
          eventType: "impressions",
          createdAt: { gte: startDate },
        },
        _sum: { value: true },
      }),

      // Total engagement
      prisma.analyticsEvent.aggregate({
        where: {
          tenantId: session.user.tenantId,
          eventType: { in: ["likes", "comments", "shares", "clicks"] },
          createdAt: { gte: startDate },
        },
        _sum: { value: true },
      }),

      // Recent posts
      prisma.post.findMany({
        where: {
          tenantId: session.user.tenantId,
          createdAt: { gte: startDate },
        },
        include: {
          campaign: { select: { name: true } },
          user: { select: { name: true } },
        },
        orderBy: { createdAt: "desc" },
        take: 10,
      }),

      // Campaign performance
      prisma.campaign.findMany({
        where: {
          tenantId: session.user.tenantId,
          status: "active",
        },
        select: {
          id: true,
          name: true,
          budget: true,
          spent: true,
          metrics: true,
          startDate: true,
          endDate: true,
        },
        orderBy: { createdAt: "desc" },
        take: 5,
      }),

      // Platform metrics
      prisma.analyticsEvent.groupBy({
        by: ["platform"],
        where: {
          tenantId: session.user.tenantId,
          createdAt: { gte: startDate },
        },
        _sum: { value: true },
        _count: true,
      }),

      // Engagement trend (daily)
      prisma.$queryRaw`
        SELECT 
          DATE(created_at) as date,
          SUM(CASE WHEN event_type IN ('likes', 'comments', 'shares', 'clicks') THEN value ELSE 0 END) as engagement,
          SUM(CASE WHEN event_type = 'impressions' THEN value ELSE 0 END) as impressions
        FROM analytics_events 
        WHERE tenant_id = ${session.user.tenantId}
          AND created_at >= ${startDate}
        GROUP BY DATE(created_at)
        ORDER BY date DESC
        LIMIT 30
      `,
    ])

    // Calculate engagement rate
    const impressions = totalImpressions._sum.value || 0
    const engagement = totalEngagement._sum.value || 0
    const engagementRate = impressions > 0 ? (engagement / impressions) * 100 : 0

    // Calculate average cost per engagement
    const spent = totalSpent._sum.spent || 0
    const costPerEngagement = engagement > 0 ? spent / engagement : 0

    const overview = {
      campaigns: {
        total: totalCampaigns,
        active: activeCampaigns,
        growth: 0, // TODO: Calculate growth percentage
      },
      posts: {
        total: totalPosts,
        published: publishedPosts,
        scheduled: totalPosts - publishedPosts,
      },
      metrics: {
        impressions: impressions,
        engagement: engagement,
        engagementRate: Math.round(engagementRate * 100) / 100,
        spent: spent,
        costPerEngagement: Math.round(costPerEngagement * 100) / 100,
      },
    }

    return NextResponse.json({
      overview,
      recentPosts,
      campaignPerformance,
      platformMetrics: platformMetrics.map((metric) => ({
        platform: metric.platform,
        value: metric._sum.value || 0,
        count: metric._count,
      })),
      engagementTrend: Array.isArray(engagementTrend) ? engagementTrend : [],
    })
  } catch (error) {
    console.error("Failed to fetch dashboard analytics:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { encrypt } from "@/lib/encryption"
import { z } from "zod"

const updateProviderSchema = z.object({
  name: z.string().min(1).max(100).optional(),
  apiKey: z.string().min(1).optional(),
  baseUrl: z.string().url().optional(),
  models: z.array(z.string()).optional(),
  settings: z.record(z.any()).optional(),
  isActive: z.boolean().optional(),
})

export async function PUT(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.tenantId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = updateProviderSchema.parse(body)

    // Verify provider belongs to tenant
    const existingProvider = await prisma.aiProvider.findFirst({
      where: {
        id: params.id,
        tenantId: session.user.tenantId,
      },
    })

    if (!existingProvider) {
      return NextResponse.json({ error: "Provider not found" }, { status: 404 })
    }

    // Prepare update data
    const updateData: any = { ...validatedData }
    if (validatedData.apiKey) {
      updateData.apiKeyEncrypted = encrypt(validatedData.apiKey)
      delete updateData.apiKey
    }

    const provider = await prisma.aiProvider.update({
      where: { id: params.id },
      data: updateData,
      select: {
        id: true,
        name: true,
        providerType: true,
        models: true,
        settings: true,
        isActive: true,
        healthStatus: true,
        lastHealthCheck: true,
        createdAt: true,
        updatedAt: true,
      },
    })

    return NextResponse.json(provider)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: "Validation error", details: error.errors }, { status: 400 })
    }

    console.error("Failed to update AI provider:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.tenantId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Verify provider belongs to tenant
    const existingProvider = await prisma.aiProvider.findFirst({
      where: {
        id: params.id,
        tenantId: session.user.tenantId,
      },
    })

    if (!existingProvider) {
      return NextResponse.json({ error: "Provider not found" }, { status: 404 })
    }

    await prisma.aiProvider.delete({
      where: { id: params.id },
    })

    return NextResponse.json({ message: "Provider deleted successfully" })
  } catch (error) {
    console.error("Failed to delete AI provider:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

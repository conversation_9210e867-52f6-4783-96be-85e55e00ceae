import { generateText, generateObject } from "ai"
import { openai } from "@ai-sdk/openai"
import { anthropic } from "@ai-sdk/anthropic"
import { google } from "@ai-sdk/google"
import { mistral } from "@ai-sdk/mistral"
import type { z } from "zod"
import { prisma } from "./prisma"
import { decrypt } from "./encryption"

export interface AIProviderConfig {
  id: string
  name: string
  type: string
  apiKey: string
  baseUrl?: string
  models: string[]
}

export class AIProviderManager {
  private providers: Map<string, any> = new Map()

  async initializeProviders(tenantId: string) {
    const dbProviders = await prisma.aiProvider.findMany({
      where: { tenantId, isActive: true },
    })

    for (const provider of dbProviders) {
      const apiKey = decrypt(provider.apiKeyEncrypted)

      switch (provider.providerType) {
        case "openai":
          this.providers.set(provider.id, openai)
          break
        case "anthropic":
          this.providers.set(provider.id, anthropic)
          break
        case "google":
          this.providers.set(provider.id, google)
          break
        case "mistral":
          this.providers.set(provider.id, mistral)
          break
      }
    }
  }

  async generateContent(
    providerId: string,
    model: string,
    prompt: string,
    options: {
      system?: string
      temperature?: number
      maxTokens?: number
    } = {},
    userId: string, // Add userId parameter
  ) {
    const provider = this.providers.get(providerId)
    if (!provider) {
      throw new Error(`Provider ${providerId} not found`)
    }

    try {
      const result = await generateText({
        model: provider(model),
        prompt,
        system: options.system,
        temperature: options.temperature,
        maxTokens: options.maxTokens,
      })

      // Track usage with real user ID
      await this.trackUsage(providerId, model, result.usage, userId)

      return result
    } catch (error) {
      console.error(`AI generation failed for provider ${providerId}:`, error)

      // Update provider health status on failure
      await prisma.aiProvider.update({
        where: { id: providerId },
        data: {
          healthStatus: "degraded",
          lastHealthCheck: new Date(),
        },
      })

      throw error
    }
  }

  async generateStructuredContent<T>(
    providerId: string,
    model: string,
    prompt: string,
    schema: z.ZodSchema<T>,
    options: {
      system?: string
      temperature?: number
    } = {},
  ) {
    const provider = this.providers.get(providerId)
    if (!provider) {
      throw new Error(`Provider ${providerId} not found`)
    }

    try {
      const result = await generateObject({
        model: provider(model),
        prompt,
        schema,
        system: options.system,
        temperature: options.temperature,
      })

      // Track usage
      await this.trackUsage(providerId, model, result.usage, "system")

      return result
    } catch (error) {
      console.error(`Structured AI generation failed for provider ${providerId}:`, error)
      throw error
    }
  }

  private async calculateCost(provider: string, model: string, tokens: number): Promise<number> {
    // Get real-time pricing from database or provider API
    const pricing = await prisma.aiProvider.findFirst({
      where: {
        providerType: provider,
        models: { path: "$", array_contains: [model] },
      },
      select: { settings: true },
    })

    if (pricing?.settings?.pricing?.[model]) {
      const rate = pricing.settings.pricing[model].inputTokens || 0.000001
      return rate * tokens
    }

    // Fallback to provider's official pricing API
    try {
      const pricingResponse = await fetch(`https://api.${provider}.com/v1/pricing/${model}`, {
        headers: { Authorization: `Bearer ${process.env[`${provider.toUpperCase()}_API_KEY`]}` },
      })

      if (pricingResponse.ok) {
        const pricingData = await pricingResponse.json()
        return pricingData.inputTokenPrice * tokens
      }
    } catch (error) {
      console.error(`Failed to fetch pricing for ${provider}:${model}`, error)
    }

    // Last resort: log for manual pricing update
    console.warn(`Unknown pricing for ${provider}:${model}, using minimal rate`)
    return 0.000001 * tokens
  }

  private async trackUsage(providerId: string, model: string, usage: any, userId: string) {
    const provider = await prisma.aiProvider.findUnique({
      where: { id: providerId },
    })

    if (provider) {
      const cost = await this.calculateCost(provider.providerType, model, usage.totalTokens || 0)

      await prisma.aiUsage.create({
        data: {
          tenantId: provider.tenantId,
          userId: userId, // Real user ID passed from request
          provider: provider.providerType,
          model,
          tokensUsed: usage.totalTokens || 0,
          cost,
          requestType: "text_generation",
        },
      })

      // Update provider health status based on usage
      await prisma.aiProvider.update({
        where: { id: providerId },
        data: {
          healthStatus: "healthy",
          lastHealthCheck: new Date(),
        },
      })
    }
  }
}

export const aiProviderManager = new AIProviderManager()
